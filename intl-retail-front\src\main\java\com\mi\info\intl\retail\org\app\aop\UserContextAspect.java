package com.mi.info.intl.retail.org.app.aop;

import com.mi.info.intl.retail.org.domain.util.ContextUtil;
import com.xiaomi.nr.global.dev.base.CommonValue;
import com.xiaomi.nr.global.dev.base.HeraContextKeyValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class UserContextAspect {

    @Before("execution(* com.mi.info.intl.retail.org.app.service.position.PositionInspectionServiceImpl.*(..))")
    public void doBefore(JoinPoint joinPoint) {
        try {
            String className = joinPoint.getTarget().getClass().getSimpleName();
            String methodName = joinPoint.getSignature().getName();
            log.info("method {}.{} invoked. ", className, methodName);

            HeraContextKeyValueHolder.setLanguageForDev(ContextUtil.getLang());
            HeraContextKeyValueHolder.put(CommonValue.KEY_AREA, ContextUtil.getArea());
        } catch (Exception e) {
            log.error("UserContextAspect.doBefore error:", e);
        }
    }

    @After("execution(* com.mi.info.intl.retail.org.app.service.position.PositionInspectionServiceImpl.*(..))")
    public void doAfter() {
        HeraContextKeyValueHolder.clear();
    }
}
