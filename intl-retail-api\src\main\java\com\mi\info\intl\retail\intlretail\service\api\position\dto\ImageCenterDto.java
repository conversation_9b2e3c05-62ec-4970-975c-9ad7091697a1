package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * @Description 提供给图片中心的DTO
 * <AUTHOR>
 * @Date 2025/7/10 17:09
 */
@Data
public class ImageCenterDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 阵地建设类型
     */
    private String positionConstructionType;
    
    /**
     * 门店大门照片链接列表
     */
    private List<String> storeGate;
    
    /**
     * 阵地落位照片链接列表
     */
    private List<String> positionLandingPhoto;
    
    /**
     * 阵地展示照片链接列表
     */
    private List<String> positionDisplay;
    
    /**
     * 家具照片链接列表
     */
    private List<String> furniturePictures;
    
    /**
     * 创建时间
     */
    private Long createdOn;
    
    /**
     * 修改时间
     */
    private Long modifiedOn;
}
