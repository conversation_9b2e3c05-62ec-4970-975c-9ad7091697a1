package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 阵地巡检审批请求DTO
 */
@Data
public class PositionInspectionApproveRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    
    /**
     * 验证操作
     * 1 代表 Approve，2 代表 disapprove
     */
    private Integer verifyAction;
    
    /**
     * 备注
     * 拒绝需要传理由
     */
    private String remark;
    
    /**
     * 阵地巡检ID
     */
    private Long positionInspectionId;

    /**
     * 不通过类型
     * 使用枚举DisapproveReasonEnum
     */
    private Integer reason ;
} 