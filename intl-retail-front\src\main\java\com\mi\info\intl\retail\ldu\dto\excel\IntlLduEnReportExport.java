package com.mi.info.intl.retail.ldu.dto.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ExcelIgnoreUnannotated
public class IntlLduEnReportExport implements Serializable {

    private static final long serialVersionUID = -765432109876543210L;

    @ExcelProperty("Region")
    private String region;

    @ExcelProperty("Country")
    private String country;

    @ExcelProperty("Business Region")
    private String bizRegion;

    @ExcelProperty("City")
    private String opsCity;

    @ExcelProperty("Grid")
    private String grid;

    @ExcelProperty("Retailer Code")
    @ColumnWidth(20)
    private String retailerCode;

    @ExcelProperty("Retailer Name")
    @ColumnWidth(20)
    private String retailerName;

    @ExcelProperty("Channel Type")
    @ColumnWidth(20)
    private String channelType;

    @ExcelProperty("Store Code")
    @ColumnWidth(20)
    private String storeCode;

    @ExcelProperty("Store Name")
    @ColumnWidth(20)
    private String storeName;

    @ExcelProperty("Position Code")
    @ColumnWidth(20)
    private String positionCode;

    @ExcelProperty("Position Name")
    @ColumnWidth(20)
    private String positionName;

    @ExcelProperty("Province")
    private String province;

    @ExcelProperty("City")
    private String city;

    @ExcelProperty("District")
    private String district;

    @ExcelProperty("Product Line")
    @ColumnWidth(20)
    private String productLine;

    @ExcelProperty("Product ID")
    @ColumnWidth(20)
    private String productId;

    @ExcelProperty("Product Name")
    @ColumnWidth(100)
    private String productName;

    @ExcelProperty("Project Code")
    @ColumnWidth(20)
    private String projectCode;

    @ExcelProperty("RAM(G)")
    private String ramCapacity;

    @ExcelProperty("ROM(G)")
    private String romCapacity;

    @ExcelProperty("SN")
    private String sn;

    @ExcelProperty("69Code")
    private String code69;

    @ExcelProperty("IMEI 1")
    private String imei1;

    @ExcelProperty("IMEI 2")
    private String imei2;

    @ExcelProperty("quantity")
    private Integer quantity;

    @ExcelProperty("Planned Status")
    @ColumnWidth(20)
    private String planStatusName;

    @ExcelProperty("LDU Type")
    private String lduType;

    @ExcelProperty("Mishow Installation Status")
    private String mishowStatusName;

    @ExcelProperty("Last Mishow Information Fetch Time")
    @ColumnWidth(40)
    private String latestTime;

    @ExcelProperty("Display Status")
    @ColumnWidth(20)
    private String displayStatusName;

    @ExcelProperty("Reported Distance Difference")
    @ColumnWidth(20)
    private BigDecimal reportDistance;

    @ExcelProperty("Reporter Name")
    @ColumnWidth(20)
    private String createUserName;

    @ExcelProperty("Reporter ID")
    @ColumnWidth(20)
    private String createUserId;

    @ExcelProperty("Reporter Role")
    @ColumnWidth(20)
    private String reportRole;

    @ExcelProperty("Report Time")
    @ColumnWidth(20)
    private Date createTime;

    @ExcelProperty("Update Time")
    @ColumnWidth(20)
    private Date updateTime;

    @ExcelProperty("LDU Image URL")
    @ColumnWidth(20)
    private String imageUrl;

}
