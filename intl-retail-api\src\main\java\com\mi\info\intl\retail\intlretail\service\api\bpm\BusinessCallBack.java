package com.mi.info.intl.retail.intlretail.service.api.bpm;

import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmCallBackParamDto;
import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.BpmApproveBusinessCodeEnum;

/**
 * 回调的接口类
 *
 * <AUTHOR>
 */
public interface BusinessCallBack {

    BpmApproveBusinessCodeEnum getBpmApproveTypeEnum();

    void doCallback(BpmCallBackParamDto response);

}
