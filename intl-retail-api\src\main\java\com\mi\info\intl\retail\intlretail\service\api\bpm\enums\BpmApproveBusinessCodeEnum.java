package com.mi.info.intl.retail.intlretail.service.api.bpm.enums;

import lombok.Getter;

@Getter
public enum BpmApproveBusinessCodeEnum {

    SO_UPLOAD("bpmn_1154058707884433408", "SO Update Approve", "wangxuankun", "so");

    BpmApproveBusinessCodeEnum(String modelCode, String instanceName, String emailPrefix, String businessKey) {
        this.modelCode = modelCode;
        this.instanceName = instanceName;
        this.emailPrefix = emailPrefix;
        this.businessKey = businessKey;
    }

    private final String modelCode;
    private final String instanceName;
    private final String emailPrefix;
    private final String businessKey;


}
