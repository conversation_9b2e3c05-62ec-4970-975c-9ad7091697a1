package com.mi.info.intl.retail.cooperation.downloadcenter.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.mi.info.intl.retail.cooperation.downloadcenter.config.JobConfig;
import com.mi.info.intl.retail.cooperation.downloadcenter.config.LduConfig;
import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobTriggerRequest;
import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobTriggerResponse;
import com.mi.info.intl.retail.cooperation.downloadcenter.service.JobTriggerService;
import com.xiaomi.nr.job.admin.dto.TriggerJobRequestDTO;
import com.xiaomi.nr.job.admin.service.NrJobService;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 通用任务触发服务实现类
 * 提供任务调度的通用能力，支持不同业务模块的任务调度需求
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Component
public class JobTriggerServiceImpl implements JobTriggerService {

    @Reference(check = false, group = "${nr.dubbo.group:}", interfaceClass = NrJobService.class, timeout = 3000)
    private NrJobService nrJobService;

    @Resource
    private JobConfig jobConfig;

    @Resource
    private LduConfig lduConfig;

    @Override
    public JobTriggerResponse triggerJob(JobTriggerRequest request) {
        try {
            // 参数校验
            validateRequest(request);

            // 构建触发请求
            TriggerJobRequestDTO triggerJobRequestDTO = buildTriggerRequest(request);

            // 记录请求日志
            log.info("triggerJob#request, req:{}", JSONUtil.toJsonStr(triggerJobRequestDTO));

            // 调用任务服务
            Result<String> result = nrJobService.triggerJob(triggerJobRequestDTO);

            // 记录响应日志
            log.info("triggerJob#response, resp:{}", JSONUtil.toJsonStr(result));

            // 构建响应
            return buildResponse(result);

        } catch (Exception e) {
            log.error("triggerJob failed, request:{}", JSONUtil.toJsonStr(request), e);
            return JobTriggerResponse.builder()
                    .code("500")
                    .message("任务触发失败: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public JobTriggerResponse triggerJobAsync(JobTriggerRequest request) {
        // 异步触发任务，可以在这里添加异步处理逻辑
        return triggerJob(request);
    }

    /**
     * 校验请求参数
     */
    private void validateRequest(JobTriggerRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (StringUtils.isBlank(request.getJobKey())) {
            throw new IllegalArgumentException("任务Key不能为空");
        }
        if (StringUtils.isBlank(request.getOwner())) {
            throw new IllegalArgumentException("任务负责人不能为空");
        }
    }

    /**
     * 构建触发请求
     */
    private TriggerJobRequestDTO buildTriggerRequest(JobTriggerRequest request) {
        TriggerJobRequestDTO triggerJobRequestDTO = Convert.convert(TriggerJobRequestDTO.class, request);

        // 设置项目信息
        if (request.getProjectId() != null) {
            triggerJobRequestDTO.setProjectId(request.getProjectId());
        } else {
            triggerJobRequestDTO.setProjectId(lduConfig.getProjectId());
        }
        if (StringUtils.isNotBlank(request.getProjectName())) {
            triggerJobRequestDTO.setProjectName(request.getProjectName());
        } else {
            triggerJobRequestDTO.setProjectName(lduConfig.getProjectName());
        }

        // 设置任务负责人
        triggerJobRequestDTO.setOwner(request.getOwner());

        return triggerJobRequestDTO;
    }

    /**
     * 构建响应
     */
    private JobTriggerResponse buildResponse(Result<String> result) {
        JobTriggerResponse response = JobTriggerResponse.builder()
                .code(String.valueOf(result.getCode()))
                .message("success")
                .build();

        // 处理响应状态
        if (result.getAttachments() != null) {
            String status = result.getAttachments().get("status");
            if (StringUtils.isNotBlank(status)) {
                response.setCode(status);
                response.setMessage(result.getData());
            }
        }

        // 设置任务ID
        if (StringUtils.isNotBlank(result.getData())) {
            response.setJobId(result.getData());
        }

        return response;
    }
} 