package com.mi.info.intl.retail.org.app.event;

import com.mi.info.intl.retail.org.infra.entity.PositionImageInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.File;
import java.util.Collection;

@Getter
@AllArgsConstructor
public class PositionImageBatchUploadEvent {
    private final Long miId;
    private final String email;
    private final Collection<PositionImageInfo> source;
    private final File workerDir;
}
