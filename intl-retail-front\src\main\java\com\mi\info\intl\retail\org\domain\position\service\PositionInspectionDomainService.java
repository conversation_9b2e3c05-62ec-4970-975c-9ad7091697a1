package com.mi.info.intl.retail.org.domain.position.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.ImageCenterDto;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.InspectionSummaryDTO;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.OptionalItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionFurnitureRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionImgBatchUploadRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionAllDetailDTO;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionApproveRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionDetailRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionDetailResponse;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionHistoryItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionSubmitRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionItemListRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionSelectorItemList;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterFinishTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.PositionInspectionResponsiblePersonDTO;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.infra.entity.PositionImageInfo;

import java.util.List;

/**
 * 阵地巡检领域服务接口
 */
public interface PositionInspectionDomainService {

    List<PositionInspectionItem> listPositionInspection(PositionInspectionRequest request);

    /**
     * 阵地巡检提交
     *
     * @param request- 提交入参
     * @return 提交响应
     */

    CommonApiResponse<String> submitPositionInspection(PositionInspectionSubmitRequest request);


    /**
     * 获取阵地巡检App详情
     *
     * @param request 请求参数
     * @return 阵地巡检APP详情
     */
    CommonApiResponse<PositionInspectionDetailResponse> getPositionInspectionDetail(PositionInspectionDetailRequest request);

    /**
     * 获取阵地巡检PC详情
     *
     * @param request 请求参数
     * @return 阵地巡检PC详情
     */
    CommonApiResponse<PositionInspectionAllDetailDTO> getPositionInspectionAllDetail(PositionInspectionDetailRequest request);

    /**
     * 分页查询阵地巡检信息
     *
     * @param request 查询请求
     * @return 分页结果
     */
    Page<PositionInspectionItem> pagePositionInspection(PositionInspectionRequest request);

    /**
     * 获取阵地巡检筛选项列表
     *
     * @param request 请求
     * @return 各筛选项的下拉列表
     */
    PositionSelectorItemList getSelectorList(PositionItemListRequest request);

    /**
     * 审批阵地巡检
     *
     * @param request 审批请求参数
     * @return 审批结果
     */
    String approvePositionInspection(PositionInspectionApproveRequest request);


    /**
     * 查询阵地巡检操作历史
     *
     * @param positionInspectionId 阵地巡检ID
     * @return 操作历史列表
     */
    List<PositionInspectionHistoryItem> operationHistory(Long positionInspectionId);

    /**
     * 标记任务为无需完成
     *
     * @param req 任务中心完成任务请求
     * @return 操作结果信息
     */
    String noNeedCompleteTask(TaskCenterFinishTaskReq req);

    /**
     * 完成用户当前任务动作
     *
     * @param req 任务中心完成任务请求
     * @return 操作结果信息
     */
    String outerTaskFinish(TaskCenterFinishTaskReq req);

    /**
     * 根据业务代码获取巡检记录列表
     *
     * @param businessCode 业务代码
     * @return 巡检记录领域对象列表
     */
    List<InspectionRecordDomain> getInspectionRecordsByBusinessCode(String businessCode);

    /**
     * 根据巡检记录获取巡检获取图片中心所需dto
     *
     * @param inspectionRecords 巡检记录列表
     * @return 图片中心领域对象
     */
    List<ImageCenterDto> getImageCenterData(List<InspectionRecordDomain> inspectionRecords);

    /**
     * 根据阵地编码创建巡检记录
     *
     * @param areaId 区域ID
     * @param positionCode 阵地编码
     * @param operatorId   操作人ID
     * @return 创建结果
     */
    String createInspectionByPositionCode(String areaId, String positionCode, String operatorId);

    /**
     * 查询阵地家具列表
     *
     * @param request 阵地家具查询请求参数
     * @return 阵地家具列表
     */
    List<OptionalItem<Integer>> getPositionFurnitureList(PositionFurnitureRequest request);

    boolean hasUnCompletedTask(String account);

    /**
     * 获取阵地巡检负责人
     *
     * @param positionCode 阵地代码
     * @return 负责人信息
     */
    PositionInspectionResponsiblePersonDTO getPositionInspectionResponsiblePerson(String positionCode);

    /**
     * 根据阵地代码获取负责人信息
     *
     * @param positionCode 阵地代码
     * @return 负责人信息
     */
    PositionInspectionResponsiblePersonDTO getResponsiblePersonByPositionCode(String positionCode);

    /**
     * 定时任务提醒
     *
     * @return 处理结果
     */
    CommonApiResponse<String> taskReminder();

    /**
     * 批量上传弹窗
     *
     * @param request 批量上传请求
     */
    void batchUpload(PositionImgBatchUploadRequest request);

    /**
     * 阵地巡检任务下发
     * 流程：1.调用getCountriesAtMidnight获取国家列表
     * 2.通过getPendingInspectionsByCountries获取未下发的巡检任务
     * 3.通过巡检任务的门店code获取门店负责人，对比巡检任务现有负责人并更新
     * 4.下发任务调用接口createInstance
     *
     * @return 处理结果
     */
    CommonApiResponse<String> dispatchInspectionTasks(Integer regularTime);

    InspectionSummaryDTO getInspectionSummary(PositionInspectionRequest request);

    void updateInspectionRecordAndFinishTask(PositionImageInfo img);
}
