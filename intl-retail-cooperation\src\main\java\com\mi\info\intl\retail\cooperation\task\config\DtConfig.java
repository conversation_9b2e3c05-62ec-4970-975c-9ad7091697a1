package com.mi.info.intl.retail.cooperation.task.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@NacosPropertySource(dataId = "bi-dt-config", autoRefreshed = true)
public class DtConfig {

    @NacosValue(value = "${bi.examBorder.secretKey:037033f763864311ae9bb8729161ccfd}", autoRefreshed = true)
    private String examSecretKey;

    @NacosValue(value = "${bi.train.secretKey:037033f763864311ae9bb8729161ccfd}", autoRefreshed = true)
    private String trainSecretKey;

    public String getExamSecretKey() {
        return examSecretKey;
    }

    public String getTrainSecretKey() {
        return trainSecretKey;
    }
}