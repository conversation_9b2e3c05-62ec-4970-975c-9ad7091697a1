package com.mi.info.intl.retail.org.app.event.listener;

import com.mi.info.intl.retail.intlretail.service.api.position.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.DisapproveReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.VerifyActionEnum;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.app.event.UpdateApprovalStatusEvent;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.org.app.event.UpdateApprovalInfo;
import com.mi.info.intl.retail.org.domain.InspectionHistoryDomain;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.PositionDomain;
import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.domain.enums.OperationType;
import com.mi.info.intl.retail.org.domain.repository.InspectionHistoryRepository;
import com.mi.info.intl.retail.org.domain.repository.InspectionRecordRepository;
import com.mi.info.intl.retail.org.domain.repository.PositionRepository;
import com.mi.info.intl.retail.org.domain.repository.RuleConfigRepository;
import com.mi.info.intl.retail.org.infra.rpc.TaskCenterServiceRpc;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.mesh.RpcContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.time.Instant;

import com.mi.info.intl.retail.org.infra.http.MaindataHttp;

/**
 * 更新审核状态事件监听器
 */
@Slf4j
@Component
public class UpdateApprovalStatusEventListener {

    @Autowired
    private InspectionRecordRepository inspectionRecordRepository;
    
    @Autowired
    private InspectionHistoryRepository inspectionHistoryRepository;

    @Autowired
    private MaindataHttp maindataHttp;

    @Autowired
    private RuleConfigRepository ruleConfigRepository;

    @Autowired
    private TaskCenterServiceRpc taskCenterServiceRpc;
    @Autowired
    private PositionRepository positionRepository;

    /**
     * 处理更新审核状态事件
     *
     * @param event 更新审核状态事件
     */
    @EventListener
    public void handleUpdateApprovalStatusEvent(UpdateApprovalStatusEvent event) {
        UpdateApprovalInfo updateApprovalInfo = event.getUpdateApprovalInfo();
        log.info("接收到更新审核状态事件: {}", updateApprovalInfo);
        
        try {
            // 获取阵地巡检ID
            Long positionInspectionId = updateApprovalInfo.getPositionInspectionId();
            // 获取验证操作
            Integer verifyAction = updateApprovalInfo.getVerifyAction();
            // 获取备注
            String remark = updateApprovalInfo.getRemark();
            // 获取拒绝原因类型
            Integer reason = updateApprovalInfo.getReason();

            // 根据阵地巡检ID查询记录
            InspectionRecordDomain inspectionRecord = inspectionRecordRepository.getById(positionInspectionId);
            if (inspectionRecord == null) {
                log.error("未找到阵地巡检记录, positionInspectionId={}", positionInspectionId);
                return;
            }
            // 获取当前任务状态和巡检状态
            TaskStatusEnum taskStatus = inspectionRecord.getTaskStatus();
            InspectionStatusEnum inspectionStatus = inspectionRecord.getInspectionStatus();

            OperationType operationType = null;
            VerifyActionEnum verifyActionEnum = VerifyActionEnum.fromCode(verifyAction);
            // 获取当前时间,到毫秒级
            Instant utcInstant = Instant.now().truncatedTo(ChronoUnit.MILLIS);
            // 根据验证操作更新状态
            if (verifyActionEnum.equals(VerifyActionEnum.APPROVE)) { // Approve操作
                log.info("执行审批通过操作, positionInspectionId={}, taskStatus={}, inspectionStatus={}",
                        positionInspectionId, taskStatus, inspectionStatus);
                // 更新巡检状态为核验通过
                inspectionRecord.setInspectionStatus(InspectionStatusEnum.VERIFICATION_PASSED); // 1: 核验通过
                inspectionRecord.setModifiedOn(utcInstant.toEpochMilli());
                inspectionRecord.setVerifier(updateApprovalInfo.getVerifier());
                inspectionRecord.setVerifierMiid(updateApprovalInfo.getVerifierMiid());
                inspectionRecord.setVerificationTime(utcInstant.toEpochMilli());
                operationType = OperationType.VERIFICATION_SUCCESS;

            } else if (verifyActionEnum.equals(VerifyActionEnum.DISAPPROVE)) { // Disapprove操作
                // 对于拒绝操作，不再判断当前taskStatus，直接更新为未完成状态
                log.info("执行审批拒绝操作, positionInspectionId={}, 当前taskStatus={}, inspectionStatus={}, 拒绝理由: {}, 拒绝原因类型: {}",
                        positionInspectionId, taskStatus, inspectionStatus, remark, reason);
                // 更新巡检状态为核验未通过，同时更新任务状态为未完成
                inspectionRecord.setInspectionStatus(InspectionStatusEnum.VERIFICATION_FAILED); // 2: 核验未通过
                inspectionRecord.setTaskStatus(TaskStatusEnum.NOT_COMPLETED); // 0: 未完成
                inspectionRecord.setRemark(remark);
                inspectionRecord.setDisapproveReason(DisapproveReasonEnum.getByCode(reason));
                inspectionRecord.setModifiedOn(utcInstant.toEpochMilli());
                inspectionRecord.setVerifier(updateApprovalInfo.getVerifier());
                inspectionRecord.setVerifierMiid(updateApprovalInfo.getVerifierMiid());
                inspectionRecord.setVerificationTime(utcInstant.toEpochMilli());
                inspectionRecord.setTaskCompletionTime(0L);
                operationType = OperationType.VERIFICATION_FAILED;
            } else {
                log.error("不支持的验证操作: {}", verifyAction);
                return;
            }
            
            // 更新记录
            boolean updateResult = inspectionRecordRepository.update(inspectionRecord);
            if (updateResult) {
                log.info("成功更新阵地巡检记录状态, positionInspectionId={}, verifyAction={}, 新taskStatus={}, 新inspectionStatus={}",
                        positionInspectionId, verifyAction, inspectionRecord.getTaskStatus(), inspectionRecord.getInspectionStatus());

                // 写入历史记录
                try {
                    InspectionHistoryDomain historyDomain = new InspectionHistoryDomain();
                    historyDomain.setInspectionRecordId(inspectionRecord.getId());
                    historyDomain.setOperationType(operationType);
                    historyDomain.setRemark(remark);
                    if (verifyActionEnum.equals(VerifyActionEnum.DISAPPROVE)) {
                        historyDomain.setDisapproveReason(reason);
                    }
                    historyDomain.setOperator(inspectionRecord.getModifiedBy());
                    historyDomain.setOperationTime(utcInstant.toEpochMilli());
                    historyDomain.setCreateTime(utcInstant.toEpochMilli());

                    boolean saveHistoryResult = inspectionHistoryRepository.save(historyDomain);
                    if (saveHistoryResult) {
                        log.info("成功写入阵地巡检历史记录, inspectionRecordId={}, operationType={}",
                                inspectionRecord.getId(), operationType);
                    } else {
                        log.error("写入阵地巡检历史记录失败, inspectionRecordId={}, operationType={}",
                                inspectionRecord.getId(), operationType);
                    }
                } catch (Exception e) {
                    log.error("写入阵地巡检历史记录异常: {}", e.getMessage(), e);
                }
            } else {
                log.error("更新阵地巡检记录状态失败, positionInspectionId={}, verifyAction={}",
                        positionInspectionId, verifyAction);
            }
            // 核验通过时，调用主数据更新状态和图片
            if (verifyActionEnum.equals(VerifyActionEnum.APPROVE) && operationType == OperationType.VERIFICATION_SUCCESS) {
                try {
                    // 解析uploadData中的图片数据
                    log.info("开始调用主数据更新阵地图片信息, positionInspectionId={}", positionInspectionId);

                    // 获取阵地编码
                    String positionCode = inspectionRecord.getPositionCode();

                    // 解析uploadData JSON
                    String uploadData = inspectionRecord.getUploadData();
                    if (uploadData == null || uploadData.isEmpty()) {
                        log.warn("阵地巡检记录无上传数据, positionInspectionId={}", positionInspectionId);
                        return;
                    }

                    // 解析JSON并构建请求参数
                    try {
                        com.alibaba.fastjson.JSONObject uploadDataJson = com.alibaba.fastjson.JSON.parseObject(uploadData);

                        // 准备调用主数据的请求参数
                        List<PositionRequest> positionRequests = new ArrayList<>();
                        PositionRequest positionRequest = new PositionRequest();

                        // 设置阵地编码
                        positionRequest.setPositionCode(positionCode);

                        // 设置巡检状态为核验通过
                        positionRequest.setInspectionStatus(InspectionStatusEnum.VERIFICATION_PASSED.getCode());

                        // 设置操作人
                        positionRequest.setOperator(String.valueOf(updateApprovalInfo.getVerifierMiid()));

                        // 构建PositionExtension对象
                        PositionExtension positionExtension = new PositionExtension();

                        // 构建PositionImage对象
                        PositionImage positionImage = new PositionImage();
                        PositionUploadDataDTO uploadDataDTO = com.alibaba.fastjson.JSON.parseObject(uploadData, PositionUploadDataDTO.class);
                        if (uploadDataDTO.getStoreGate() != null) {
                            positionImage.setStoreGate(uploadDataDTO.getStoreGate().getImages());
                        }
                        if (uploadDataDTO.getPositionDisplay() != null) {
                            positionImage.setPositionDisplay(uploadDataDTO.getPositionDisplay().getImages());
                        }
                        if (uploadDataDTO.getFurniturePictures() != null) {
                            List<String> furniturePictures = new ArrayList<>();
                            for (PositionUploadDataDTO.ImageGroup group : uploadDataDTO.getFurniturePictures()) {
                                if (group.getImages() != null) {
                                    furniturePictures.addAll(group.getImages());
                                }
                            }
                            positionImage.setFurniturePicture(furniturePictures);
                        }
                        if (uploadDataDTO.getPositionLandingPhoto() != null) {
                            positionImage.setPositionLandingPicture(uploadDataDTO.getPositionLandingPhoto().getImages());
                        }

                        // 设置positionImage到positionExtension
                        positionExtension.setPositionImage(positionImage);

                        // 设置positionExtension到positionRequest
                        positionRequest.setPositionExtension(positionExtension);

                        // 添加到请求列表
                        positionRequests.add(positionRequest);

                        // 调用主数据更新接口
                        PositionResponse response = maindataHttp.editPositionInfo(positionRequests);

                        if (response != null) {
                            log.info("调用主数据更新阵地图片信息成功, positionInspectionId={}, responseCode={}, responseMessage={}",
                                    positionInspectionId, response.getCode(), response.getMessage());
                        } else {
                            log.warn("调用主数据更新阵地图片信息返回为空, positionInspectionId={}", positionInspectionId);
                        }
                    } catch (Exception e) {
                        log.error("解析uploadData或调用主数据更新阵地图片信息异常: {}", e.getMessage(), e);
                    }
                } catch (Exception e) {
                    log.error("调用主数据更新阵地图片信息失败: {}", e.getMessage(), e);
                }
            }

            // 核验不通过时，调用中央大脑未完成接口
            if (verifyActionEnum.equals(VerifyActionEnum.DISAPPROVE) && operationType == OperationType.VERIFICATION_FAILED) {
                try {
                    log.info("开始调用中央大脑未完成接口, positionInspectionId={}", positionInspectionId);

                    // 获取规则编码
                    String ruleCode = inspectionRecord.getRuleCode();

                    // 查询规则配置，获取taskBatchId
                    RuleConfigDomain ruleConfig = ruleConfigRepository.getByRuleCode(ruleCode);

                    if (ruleConfig == null || ruleConfig.getTaskBatchId() == null) {
                        log.warn("找不到规则配置或taskBatchId为空, ruleCode={}", ruleCode);
                        return;
                    }

                    // 创建TaskCenterTaskReq对象
                    TaskCenterTaskReq taskCenterTaskReq = new TaskCenterTaskReq();

                    // 设置mid为inspection_owner_miId
                    taskCenterTaskReq.setMid(inspectionRecord.getInspectionOwnerMiId());

                    // 获取阵地信息
                    PositionDomain positionDomain = positionRepository.getByPositionCode(inspectionRecord.getPositionCode());
                    if (positionDomain == null) {
                        log.error("阵地信息不存在: positionCode={}", inspectionRecord.getPositionCode());
                        return;
                    }
                    // 获取阵地编码 SELECT code FROM `intl_rms_position` where  crpscode='CID001915'
                    taskCenterTaskReq.setOrgId(positionDomain.getPositionCode());

                    // 设置taskBatchId为intl_inspection_rule的task_batch_id
                    taskCenterTaskReq.setTaskBatchId(ruleConfig.getTaskBatchId());

                    // 调用中央大脑接口
                    taskCenterServiceRpc.reloadTaskStatus(taskCenterTaskReq);

                    log.info("成功调用中央大脑未完成接口, positionInspectionId={}, mid={}, orgId={}, taskBatchId={}",
                            positionInspectionId, taskCenterTaskReq.getMid(), taskCenterTaskReq.getOrgId(), taskCenterTaskReq.getTaskBatchId());
                } catch (Exception e) {
                    log.error("调用中央大脑未完成接口失败: {}", e.getMessage(), e);
                }
            }

        } catch (Exception e) {
            log.error("处理更新审核状态事件失败: {}", e.getMessage(), e);
        }



    }
} 