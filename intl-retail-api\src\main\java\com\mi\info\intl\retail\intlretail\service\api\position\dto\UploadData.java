package com.mi.info.intl.retail.intlretail.service.api.position.dto;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description 提交巡检图片的DTO
 * <AUTHOR>
 * @Date 2025/7/9 21:05
 */
@Data
public class UploadData {
    /**
     * 门店大门照片
     */
    private PhotoGroup storeGate;

    /**
     * 阵地落位照片
     */
    private PhotoGroup positionLandingPhoto;

    /**
     * 阵地展示照片
     */
    private PhotoGroup positionDisplay;

    /**
     * 家具照片列表
     */
    private List<FurniturePhotoGroup> furniturePictures;

    public List<PhotoGroup> flatMap() {
        return Stream.concat(Stream.of(storeGate, positionLandingPhoto, positionDisplay),
                        Optional.ofNullable(furniturePictures).orElse(Collections.emptyList()).stream())
                .filter(Objects::nonNull).collect(Collectors.toList());
    }
}
