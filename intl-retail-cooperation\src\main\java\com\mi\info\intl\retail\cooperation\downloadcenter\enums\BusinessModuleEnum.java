package com.mi.info.intl.retail.cooperation.downloadcenter.enums;

import lombok.Getter;

/**
 * 业务模块枚举
 * 定义支持的业务模块类型
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Getter
public enum BusinessModuleEnum {

    COMMON_EXPORT("intl-retail", "通用文件导出", "Common Export Job", "下载通用文件信息", "commonExportHandleXxlJob", 11L, "intl-retail");
    private final String businessModule;
    private final String taskName;
    private final String taskDesc;
    private final String description;
    private final String jobKey;

    private final Long projectId;
    private final String projectName;

    BusinessModuleEnum(String businessModule, String taskName, String taskDesc, String description, String jobKey, Long projectId, String projectName) {
        this.businessModule = businessModule;
        this.taskName = taskName;
        this.taskDesc = taskDesc;
        this.description = description;
        this.jobKey = jobKey;
        this.projectId = projectId;
        this.projectName = projectName;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 模块代码
     * @return 业务模块枚举
     */
    public static BusinessModuleEnum getByCode(String code) {
        for (BusinessModuleEnum module : values()) {
            if (module.getBusinessModule().equals(code)) {
                return module;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效的模块代码
     *
     * @param code 模块代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
} 