package com.mi.info.intl.retail.org.domain.position.service.impl;

import com.mi.info.intl.retail.org.domain.position.service.RuleConfigDomainService;
import com.mi.info.intl.retail.org.domain.repository.RuleConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 阵地配置领域服务实现类
 */
@Slf4j
@Service
public class RuleConfigDomainServiceImpl implements RuleConfigDomainService {

    @Resource
    private RuleConfigRepository ruleConfigRepository;
    
}