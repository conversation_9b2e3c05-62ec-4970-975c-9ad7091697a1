package com.mi.info.intl.retail.org.app.event;

import com.mi.info.intl.retail.intlretail.service.api.position.enums.DisapproveReasonEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 更新审核状态的信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateApprovalInfo {
    /**
     * 验证操作：1 代表 Approve，2 代表 disapprove
     */
    private Integer verifyAction;
    
    /**
     * 备注（拒绝需要传理由）
     */
    private String remark;
    
    /**
     * 阵地巡检ID
     */
    private Long positionInspectionId;
    /**
     * 不通过类型
     * 使用枚举DisapproveReasonEnum
     */
    private Integer reason ;
    private String verifier;
    private Long verifierMiid;
} 