package com.mi.info.intl.retail.org.app.event;

import org.springframework.context.ApplicationEvent;

public class UpdateApprovalStatusEvent extends ApplicationEvent {
    private UpdateApprovalInfo updateApprovalInfo;

    public UpdateApprovalStatusEvent(Object source, UpdateApprovalInfo updateApprovalInfo) {
        super(source);
        this.updateApprovalInfo = updateApprovalInfo;
    }

    public UpdateApprovalInfo getUpdateApprovalInfo() {
        return updateApprovalInfo;
    }

    public void setUpdateApprovalInfo(UpdateApprovalInfo updateApprovalInfo) {
        this.updateApprovalInfo = updateApprovalInfo;
    }
} 