package com.mi.info.intl.retail.intlretail.service.api.position.enums.seria;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;

import java.io.IOException;

public class I18nDescToIntSerializer extends JsonSerializer<I18nDesc> {
    @Override
    public void serialize(I18nDesc i18nDesc, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
            throws IOException {
        jsonGenerator.writeNumber(I18nDesc.safeGetCode(i18nDesc));
    }
}
