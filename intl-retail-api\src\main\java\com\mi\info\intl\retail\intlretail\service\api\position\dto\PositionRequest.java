package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Position request for HTTP API
 */
@Data
public class PositionRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    private String positionCode;
    private PositionExtension positionExtension;
    private String operator;
    private Integer inspectionStatus;


}
