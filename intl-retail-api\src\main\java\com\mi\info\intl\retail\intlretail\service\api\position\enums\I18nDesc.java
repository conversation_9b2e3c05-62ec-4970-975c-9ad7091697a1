package com.mi.info.intl.retail.intlretail.service.api.position.enums;

import com.mi.info.intl.retail.intlretail.service.api.position.dto.OptionalItem;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public interface I18nDesc {

    Integer getCode();

    Supplier<String> getI18nDesc();

    static <T extends Enum<T> & I18nDesc> List<OptionalItem<Integer>> toOptionalItems(Class<T> clz) {
        return Arrays.stream(clz.getEnumConstants()).map(e -> new OptionalItem<>(e.getCode(), e.getI18nDesc().get()))
                .collect(Collectors.toList());
    }

    default String getDesc() {
        return getI18nDesc().get();
    }

    static String safeGetDesc(I18nDesc i18nDesc) {
        return safeGetDesc(i18nDesc, null);
    }

    static String safeGetDesc(I18nDesc i18nDesc, String defaultValue) {
        return i18nDesc == null ? defaultValue : i18nDesc.getDesc();
    }

    static Integer safeGetCode(I18nDesc i18nDesc) {
        return i18nDesc == null ? null : i18nDesc.getCode();
    }

    static <T extends Enum<T> & I18nDesc> String getDescByCode(Class<T> clz, Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(clz.getEnumConstants()).filter(e -> Objects.equals(e.getCode(), code)).findFirst()
                .map(I18nDesc::getDesc).orElse(null);
    }
}
