package com.mi.info.intl.retail.intlretail.service.api.upload;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public class FileUploadInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<MetaData> metaDataList;

    /**
     * 业务关联ID
     */
    private Long relatedId;

    /**
     * 标记是否为离线上传
     */
    private boolean offlineUpload;

    /**
     * 标记是否已上传到Blob
     */
    private boolean uploadedToBlob;

    /**
     * 所属模块名称（如ldu_upload）
     */
    private String moduleName;

    /**
     * 上传者用户名
     */
    private String uploaderName;

    /**
     * 文件水印标记：0-需要水印，1-无需水印
     */
    private boolean noWatermark;

    @AllArgsConstructor
    @Getter
    public static class MetaData implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 文件全局唯一标识
         */
        private String guid;

        /**
         * 文件上传地址列表
         */
        private List<String> fdsUrls;
    }

    public static FileUploadInfo fromRequest(FileUploadRequest request) {
        FileUploadInfo fileUploadInfo = new FileUploadInfo();
        FileUploadRequest.Photo photo1 = request.getPhotos().get(0);
        String moduleName = photo1.getModuleName();
        fileUploadInfo.setUploaderName(photo1.getUploaderName());
        fileUploadInfo.setModuleName(moduleName);
        fileUploadInfo.setOfflineUpload(photo1.isOfflineUpload());
        fileUploadInfo.setUploadedToBlob(photo1.isUploadedToBlob());
        fileUploadInfo.setNoWatermark(photo1.isNoNeedWatermark());
        Map<String, List<String>> metaDataMap = new HashMap<>();
        for (FileUploadRequest.Photo photo : request.getPhotos()) {
            metaDataMap.computeIfAbsent(photo.getGuid(), k -> new ArrayList<>()).add(photo.getFdsUrl());
        }
        List<FileUploadInfo.MetaData> metaDataList =
                metaDataMap.entrySet().stream().map(e -> new FileUploadInfo.MetaData(e.getKey(), e.getValue()))
                        .collect(Collectors.toList());
        fileUploadInfo.setMetaDataList(metaDataList);
        return fileUploadInfo;
    }
}
