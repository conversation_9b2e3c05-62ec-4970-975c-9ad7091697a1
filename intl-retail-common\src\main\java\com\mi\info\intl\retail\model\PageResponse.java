package com.mi.info.intl.retail.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 通用分页响应DTO
 *
 * @param <T> 列表项类型
 */
@Data
public class PageResponse<T extends Serializable> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 总记录数
     */
    private Long totalCount;
    
    /**
     * 当前页码
     */
    private Long pageNum;
    
    /**
     * 每页大小
     */
    private Long pageSize;
    
    /**
     * 数据列表
     */
    private List<T> list;

    //多参数构建
    public PageResponse(Long totalCount, Long pageNum, Long pageSize, List<T> list) {
        this.totalCount = totalCount;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.list = list;
    }
}