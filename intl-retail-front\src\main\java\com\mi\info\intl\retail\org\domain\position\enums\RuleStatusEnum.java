package com.mi.info.intl.retail.org.domain.position.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 规则状态枚举
 */
@Getter
@AllArgsConstructor
public enum RuleStatusEnum {
    
    /**
     * 无效
     */
    INVALID(0, "无效"),
    
    /**
     * 有效
     */
    VALID(1, "有效");
    
    /**
     * 状态码
     */
    private final Integer code;
    
    /**
     * 状态描述
     */
    private final String desc;
    
    /**
     * 根据code获取枚举
     *
     * @param code 状态码
     * @return 规则状态枚举
     */
    public static RuleStatusEnum getByCode(Integer code) {
        for (RuleStatusEnum statusEnum : RuleStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
} 