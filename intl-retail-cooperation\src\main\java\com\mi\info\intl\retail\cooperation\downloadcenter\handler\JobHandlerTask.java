package com.mi.info.intl.retail.cooperation.downloadcenter.handler;


import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobSuccessDto;
import com.xiaomi.nr.job.core.context.JobHelper;
import com.xiaomi.nr.job.core.handler.annotation.NrJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class JobHandlerTask {

    @NrJob("commonExportHandleXxlJob")
    public void commonExportHandleXxlJob() {
        try {
            String jobParam = JobHelper.getJobParam();
            log.info("commonExportHandleXxlJob,jobParam:{}", jobParam);
            if (CharSequenceUtil.isNotBlank(jobParam)) {
                JobSuccessDto jobSuccessDto = JSONUtil.toBean(job<PERSON>aram, JobSuccessDto.class);
                boolean handleSuccess = JobHelper.handleSuccess(JSONUtil.toJsonStr(jobSuccessDto));
                log.info("commonExportHandleXxlJob,handleSuccess:{}", handleSuccess);
            }
        } catch (Exception e) {
            log.error("commonExportHandleXxlJob 发生异常", e);
        }
    }
}
