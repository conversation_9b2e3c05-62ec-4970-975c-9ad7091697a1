package com.mi.info.intl.retail.utils;

import com.google.gson.Gson;

import java.lang.reflect.Type;

/**
 * <AUTHOR> 董鑫儒
 * @Description
 * @Date 创建于 2022-04-08, 0008 14:53:29
 */
public class GsonUtils {
    private GsonUtils() {
    }
    static Gson gson = new Gson();

    public static String toStr(Object a) {
        return gson.toJson(a);
    }

    public static <T> T toObject(String a, Class<T> clazz) {
        return gson.fromJson(a, clazz);
    }

    public static <T> T toObject(String a, Type clazz) {
        return gson.fromJson(a, clazz);
    }
}
