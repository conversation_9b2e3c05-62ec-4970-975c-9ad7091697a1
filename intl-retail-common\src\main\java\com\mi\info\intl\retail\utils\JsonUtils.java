package com.mi.info.intl.retail.utils;

import com.fasterxml.jackson.core.type.TypeReference;

import java.lang.reflect.Type;

public final class JsonUtils {
    private JsonUtils() {
    }

    public static String toStr(Object a) {
        return GsonUtils.toStr(a);
    }

    public static <T> T toObject(String a, Class<T> clazz) {
        return GsonUtils.toObject(a, clazz);
    }

    public static <T> T toObject(String a, Type type) {
        return GsonUtils.toObject(a, type);
    }

    public static <T> T toObject(String a, TypeReference<T> valueTypeRef) {
        return JacksonUtils.toObject(a, valueTypeRef);
    }
}
