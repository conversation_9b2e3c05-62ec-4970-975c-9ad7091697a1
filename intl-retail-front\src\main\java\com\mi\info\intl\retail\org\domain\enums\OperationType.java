package com.mi.info.intl.retail.org.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;
import com.xiaomi.nr.global.dev.neptune.T;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Supplier;

/**
 * 操作类型枚举
 */
@Getter
@AllArgsConstructor
public enum OperationType implements I18nDesc {
    /**
     * 创建
     */
    CREATE(0, () -> T.tr("inspection.create")),

    /**
     * 提交
     */
    SUBMIT(1, () -> T.tr("inspection.submit")),

    /**
     * 验证失败
     */
    VERIFICATION_FAILED(2, () -> T.tr("inspection.verification_failed")),

    /**
     * 验证成功
     */
    VERIFICATION_SUCCESS(3, () -> T.tr("inspection.verification_success"));

    @EnumValue
    private final Integer code;

    private final Supplier<String> i18nDesc;
}