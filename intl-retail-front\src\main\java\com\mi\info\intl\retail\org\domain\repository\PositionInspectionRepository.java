package com.mi.info.intl.retail.org.domain.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionRequest;

/**
 * 阵地巡检仓储接口
 */
public interface PositionInspectionRepository {
    
    /**
     * 分页查询阵地巡检信息
     * 
     * @param page 分页参数
     * @param request 查询请求
     * @return 分页结果
     */
    Page<PositionInspectionItem> pagePositionInspection(Page<PositionInspectionItem> page, PositionInspectionRequest request);
}
