package com.mi.info.intl.retail.ldu.app.service.impl;

import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobTriggerRequest;
import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobTriggerResponse;
import com.mi.info.intl.retail.cooperation.downloadcenter.service.JobTriggerService;
import com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.NrJobGateway;
import com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.dto.NrJobGoIn;
import com.mi.info.intl.retail.intlretail.service.api.nrjob.gateway.dto.NrJobGoOut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * NrJobGateway适配器
 * 保持向后兼容性，将原有接口适配到新的通用任务触发能力
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Component
public class NrJobGatewayAdapter implements NrJobGateway {

    @Resource
    private JobTriggerService jobTriggerService;

    @Override
    public NrJobGoOut triggerJob(NrJobGoIn nrJobGoIn) {
        try {
            // 转换为通用任务触发请求
            JobTriggerRequest request = convertToJobTriggerRequest(nrJobGoIn);
            
            // 触发任务
            JobTriggerResponse response = jobTriggerService.triggerJob(request);
            
            // 转换为原有响应格式
            return convertToNrJobGoOut(response);
            
        } catch (Exception e) {
            log.error("任务触发失败", e);
            return NrJobGoOut.builder()
                    .code("500")
                    .message("任务触发失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 转换NrJobGoIn为JobTriggerRequest
     */
    private JobTriggerRequest convertToJobTriggerRequest(NrJobGoIn nrJobGoIn) {
        return JobTriggerRequest.builder()
                .taskParam(nrJobGoIn.getTaskParam())
                .taskDesc(nrJobGoIn.getTaskDesc())
                .taskName(nrJobGoIn.getTaskName())
                .jobKey(nrJobGoIn.getJobKey())
                .owner(nrJobGoIn.getOwner())
                .traceId(nrJobGoIn.getTraceId())
                .businessModule("ldu")
                .build();
    }

    /**
     * 转换JobTriggerResponse为NrJobGoOut
     */
    private NrJobGoOut convertToNrJobGoOut(JobTriggerResponse response) {
        return NrJobGoOut.builder()
                .code(response.getCode())
                .message(response.getMessage())
                .build();
    }
} 