package com.mi.info.intl.retail.intlretail.service.api.store.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 阵地巡检负责人DTO
 *
 *  <AUTHOR>
 *  @date 2025/07/10
 */
@Data
public class PositionInspectionResponsiblePersonDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户职位
     */
    private String userTitle;

    /**
     * 用户职位代码
     */
    private Integer userTitleCode;

    /**
     * 用户ID
     */
    private Long miId;

    /**
     * 创建时间
     */
    private Date createdOn;
    
    /**
     * 语言代码
     */
    private String languageCode;
    
    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店code
     */
    private String psitionCode;
} 