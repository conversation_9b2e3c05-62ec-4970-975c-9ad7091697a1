package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class TaskCenterChangeExecutorReq implements Serializable {
    private static final long serialVersionUID = 5813200837116019198L;
    private Long mid;

    private String positionCode;

    private Long newMid;

    private Long taskBatchId;
    private String retailAppSign = "CHANNEL_RETAIL";
    private String retailTenantId = "2";
    private String languageKey = "en-US";
}

