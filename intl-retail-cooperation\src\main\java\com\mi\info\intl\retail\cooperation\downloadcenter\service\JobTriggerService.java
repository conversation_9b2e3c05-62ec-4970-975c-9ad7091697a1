package com.mi.info.intl.retail.cooperation.downloadcenter.service;

import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobTriggerRequest;
import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobTriggerResponse;

/**
 * 通用任务触发服务接口
 * 提供任务调度的通用能力，支持不同业务模块的任务调度需求
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
public interface JobTriggerService {

    /**
     * 触发任务
     *
     * @param request 任务触发请求
     * @return 任务触发响应
     */
    JobTriggerResponse triggerJob(JobTriggerRequest request);

    /**
     * 异步触发任务
     *
     * @param request 任务触发请求
     * @return 任务触发响应
     */
    JobTriggerResponse triggerJobAsync(JobTriggerRequest request);
} 