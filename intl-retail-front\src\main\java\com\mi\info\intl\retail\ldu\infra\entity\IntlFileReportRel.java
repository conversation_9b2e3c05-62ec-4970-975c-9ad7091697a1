package com.mi.info.intl.retail.ldu.infra.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@TableName(value = "file_report_rel", autoResultMap = true)
@Data
public class IntlFileReportRel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文件上传表ID
     */
    @TableField("guid")
    private String guid;

    /**
     * LDU上报日志表ID
     */
    @TableField("report_log_id")
    private Long reportLogId;

}