package com.mi.info.intl.retail.cooperation.downloadcenter.factory;

import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobTriggerRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 任务触发工厂类
 * 支持不同业务模块的配置管理
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Component
public class JobTriggerFactory {

    /**
     * 业务模块配置缓存
     */
    private static final Map<String, BusinessModuleConfig> MODULE_CONFIGS = new ConcurrentHashMap<>();

    /**
     * 注册业务模块配置
     *
     * @param moduleName 模块名称
     * @param config     模块配置
     */
    public void registerModuleConfig(String moduleName, BusinessModuleConfig config) {
        MODULE_CONFIGS.put(moduleName, config);
        log.info("注册业务模块配置成功, moduleName:{}, config:{}", moduleName, config);
    }

    /**
     * 获取业务模块配置
     *
     * @param moduleName 模块名称
     * @return 模块配置
     */
    public BusinessModuleConfig getModuleConfig(String moduleName) {
        return MODULE_CONFIGS.get(moduleName);
    }

    /**
     * 构建任务触发请求
     *
     * @param moduleName 模块名称
     * @param jobKey     任务Key
     * @param owner      负责人
     * @param taskParam  任务参数
     * @param taskDesc   任务描述
     * @param taskName   任务名称
     * @return 任务触发请求
     */
    public JobTriggerRequest buildRequest(String moduleName, String jobKey, String owner, 
                                        String taskParam, String taskDesc, String taskName) {
        BusinessModuleConfig config = getModuleConfig(moduleName);
        if (config == null) {
            throw new IllegalArgumentException("未找到业务模块配置: " + moduleName);
        }

        return JobTriggerRequest.builder()
                .jobKey(jobKey)
                .owner(owner)
                .taskParam(taskParam)
                .taskDesc(taskDesc)
                .taskName(taskName)
                .projectId(config.getProjectId())
                .projectName(config.getProjectName())
                .businessModule(moduleName)
                .build();
    }

    /**
     * 业务模块配置类
     */
    public static class BusinessModuleConfig {
        private Long projectId;
        private String projectName;
        private String description;

        public BusinessModuleConfig(Long projectId, String projectName, String description) {
            this.projectId = projectId;
            this.projectName = projectName;
            this.description = description;
        }

        // Getters and Setters
        public Long getProjectId() {
            return projectId;
        }

        public void setProjectId(Long projectId) {
            this.projectId = projectId;
        }

        public String getProjectName() {
            return projectName;
        }

        public void setProjectName(String projectName) {
            this.projectName = projectName;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        @Override
        public String toString() {
            return "BusinessModuleConfig{" +
                    "projectId=" + projectId +
                    ", projectName='" + projectName + '\'' +
                    ", description='" + description + '\'' +
                    '}';
        }
    }
} 