package com.mi.info.intl.retail.org.domain.service;

import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.domain.dto.RuleConfigWithPendingInspectionDTO;

import java.util.List;

/**
 * 规则配置服务接口
 */
public interface RuleConfigService {

    /**
     * 保存规则配置
     *
     * @param ruleConfigDomain 规则配置领域对象
     * @return 是否成功
     */
    boolean saveRuleConfig(RuleConfigDomain ruleConfigDomain);

    /**
     * 更新规则配置
     *
     * @param ruleConfigDomain 规则配置领域对象
     * @return 是否成功
     */
    boolean updateRuleConfig(RuleConfigDomain ruleConfigDomain);

    /**
     * 根据ID查询规则配置
     *
     * @param id 规则配置ID
     * @return 规则配置领域对象
     */
    RuleConfigDomain getRuleConfigById(Long id);

    /**
     * 根据规则编码查询规则配置
     *
     * @param ruleCode 规则编码
     * @return 规则配置领域对象
     */
    RuleConfigDomain getRuleConfigByRuleCode(String ruleCode);

    /**
     * 根据国家列表查询有效的规则配置和未下发的巡检记录
     *
     * @param countries 国家列表
     * @return 规则配置和未下发巡检记录数量DTO列表
     */
    List<RuleConfigWithPendingInspectionDTO> getActiveRulesWithPendingInspections(List<String> countries);
} 