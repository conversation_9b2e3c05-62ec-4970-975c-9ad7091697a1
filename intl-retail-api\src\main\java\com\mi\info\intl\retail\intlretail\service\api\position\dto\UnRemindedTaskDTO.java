package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.ConstructionType;
import lombok.Data;

import java.io.Serializable;

@Data
public class UnRemindedTaskDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 任务模板id (关联汽车中央大脑任务模板id)
     */
    private Long taskDefId;

    /**
     * 任务批次id (关联汽车中央大脑任务批次id)
     */
    private Long taskBatchId;

    /**
     * 规则编码 ruleCode
     */
    private String ruleCode;

    /**
     * 规则名称 ruleName
     */
    private String ruleName;
    /**
     * 国家
     */
    private String country;
    /**
     * 循环提醒天数
     */
    private Integer reminderDays;
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 业务唯一编码 阵地代码
     */
    private String businessCode;

    /**
     * 业务品类 职位建设类型
     */
    private ConstructionType businessType;
    /**
     * 巡检负责人MiID
     */
    private Long inspectionOwnerMiId;
    /**
     * 任务状态 未完成 已完成 无需完成
     */
    private Integer taskStatus;

    /**
     * 巡检状态 未下发 未完成 待核验 核验通过 核验未通过
     */
    private Integer inspectionStatus;
}
