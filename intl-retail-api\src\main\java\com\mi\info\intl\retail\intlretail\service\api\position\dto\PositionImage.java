package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Position image for HTTP API
 */
@Data
public class PositionImage implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<String> furniturePicture;
    private List<String> displayCapacityExpansion;
    private List<String> positionDisplay;
    private List<String> positionLandingPicture;
    private List<String> storeGate;
}
