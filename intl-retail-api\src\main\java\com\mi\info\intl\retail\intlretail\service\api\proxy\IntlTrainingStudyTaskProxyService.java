package com.mi.info.intl.retail.intlretail.service.api.proxy;

import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlBaseRequest;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlStudyProgressParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlStudyProgressUploadParam;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlStudyTaskDownLineParam;

public interface IntlTrainingStudyTaskProxyService {

    /**
     * 首页-Project
     *
     */
    Object projectList(IntlBaseRequest intlBaseRequest);

    /**
     * 首页-Course
     *
     */
    Object courseList(IntlBaseRequest intlBaseRequest);

    /**
     * 获取学习项目详情
     *
     */
    Object getStudyTaskDetail(IntlStudyTaskDownLineParam intlStudyTaskDownLineParam);

    /**
     * 获取学习项目详情
     *
     */
    Object getDetailProgress(IntlStudyProgressParam intlStudyProgressParam);

    /**
     * 上传学习进度
     *
     */
    Object uploadStudyProgress(IntlStudyProgressUploadParam intlStudyProgressUploadParam);
}
