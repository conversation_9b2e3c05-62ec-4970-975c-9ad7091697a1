package com.mi.info.intl.retail.cooperation.downloadcenter.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 通用任务配置类
 * 支持不同业务模块的任务调度配置
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@Component
@ConfigurationProperties(prefix = "job")
public class JobConfig {

    /**
     * 任务管理地址
     */
    private String addresses;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 应用名称
     */
    private String appname;

    /**
     * 端口
     */
    private Integer port = 9991;

    /**
     * 日志路径
     */
    private String logPath;

    /**
     * 日志保留天数
     */
    private int logRetentionDays = 30;

    /**
     * Dubbo分组
     */
    private String dubboGroup;

    /**
     * 超时时间（毫秒）
     */
    private int timeout = 3000;

    /**
     * 是否启用检查
     */
    private boolean check = false;
} 