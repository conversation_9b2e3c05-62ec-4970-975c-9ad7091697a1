package com.mi.info.intl.retail.intlretail.service.api.position.enums;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 阵地进行阶段枚举
 * 阶段节点跟constructionType的映射关系
 */
@Getter
@AllArgsConstructor
public enum PositionStageEnum {

    /**
     * 接受批准
     */
    ACCEPTANCE_APPROVED(7, "creation", "接受批准", ConstructionType.CREATE),

    /**
     * 升级接受批准
     */
    UPGRADE_ACCEPTANCE_APPROVED(14, "upgrade", "升级接受批准", ConstructionType.UPDATE);

    /**
     * 编码
     */
    private final Integer code;

    /**
     * 阵地阶段
     */
    private final String stage;

    /**
     * 描述
     */
    private final String description;

    private final ConstructionType constructionType;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static PositionStageEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PositionStageEnum value : PositionStageEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 