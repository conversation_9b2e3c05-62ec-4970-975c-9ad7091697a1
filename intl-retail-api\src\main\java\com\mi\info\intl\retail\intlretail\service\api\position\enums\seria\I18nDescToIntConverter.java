package com.mi.info.intl.retail.intlretail.service.api.position.enums.seria;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;

import java.math.BigDecimal;

public class I18nDescToIntConverter implements Converter<I18nDesc> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return I18nDesc.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.NUMBER;
    }

    @Override
    public WriteCellData<?> convertToExcelData(I18nDesc value, ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {
        if (value == null) {
            return new WriteCellData<>();
        }
        return new WriteCellData<>(BigDecimal.valueOf(value.getCode()));
    }

}
