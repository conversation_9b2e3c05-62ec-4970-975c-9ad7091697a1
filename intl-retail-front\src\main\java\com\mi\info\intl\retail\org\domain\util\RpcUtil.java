package com.mi.info.intl.retail.org.domain.util;

import com.xiaomi.youpin.infra.rpc.Result;
import lombok.experimental.UtilityClass;

import java.util.Optional;

@UtilityClass
public class RpcUtil {

    public static <T> T getRpcResult(Result<T> result) {
        return Optional.ofNullable(result)
                .filter(r -> r.getCode() == 0)
                .orElseThrow(() -> new RuntimeException(result == null ? "result is null" : result.getMessage()))
                .getData();
    }
}
