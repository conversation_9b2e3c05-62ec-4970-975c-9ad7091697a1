package com.mi.info.intl.retail.intlretail.service.api.proxy;

import com.xiaomi.proretail.training.api.vo.trainingcheck.intl.*;
import com.xiaomi.proretail.training.api.vo.trainingstudy.intl.IntlTrainingTaskModelVO;

import javax.servlet.http.HttpServletRequest;

public interface IntlTrainingExamProxyService {

    /**
     * 考试题目获取接口（组卷考试）
     * /mtop/proretail/market/teach/getExamProblems
     */
    IntlExamResponse<IntlProblem> getExamProblems(IntlExamRequest examRequest);

    /**
     * 考试须知页面接口
     * /mtop/proretail/market/teach/getExamInstruction
     */
    IntlInstruction getExamInstruction(IntlExamRequest examRequest);


    /**
     * 考试评分明细接口
     * /mtop/proretail/market/teach/getExamDetail
     */
    IntlExamDetailResponse getExamDetail(IntlExamRequest examRequest);


    /**
     * 提交试卷交卷接口
     * /mtop/proretail/training/exam/submit
     */
    IntlExamSubmitResponse submit(IntlExamSubmitRequest examSubmitRequest);
}
