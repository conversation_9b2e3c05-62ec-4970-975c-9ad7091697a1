package com.mi.info.intl.retail.cooperation.downloadcenter.config;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * ldu相关配置读取
 *
 * <AUTHOR>
 * @date 2025/7/21 16:24
 */
@Data
@Component
@NacosConfigurationProperties(dataId = "intl-retail-ldu-sg-common", autoRefreshed = true)
public class LduConfig {

    /**
     * 产品线配置
     */
    @NacosValue("${constant-data.productLines:}")
    private String productLines;

    /**
     * 商品主数据扩展信息id，用于按需同步扩展信息
     */
    @NacosValue("${constant-data.productExtInfoIds:'[86, 88]'}")
    private String productExtInfoIds;

    @NacosValue(value = "${countries.list:}")
    private String list;


    @Value("${job.admin.addresses:}")
    private String addresses;

    @Value("${job.accessToken:}")
    private String accessToken;


    @Value("${job.executor.appname:}")
    private String appname;


    @Value("${job.executor.port:9991}")
    private Integer port;


    @Value("${job.executor.logpath:}")
    private String logPath;


    @Value("${job.executor.logretentiondays:30}")
    private int logretentiondays;

    @Value("${proretail.project.id:11}")
    private Long projectId;

    @Value("${proretail.project.name:intl-retail}")
    private String projectName;

    @Value("${intelTemple-url.planUploadUrl}")
    private String planUploadUrl;

    @Value("${intelTemple-url.planStopUrl}")
    private String planStopUrl;

    @Value("${intelTemple-url.targetUploadUrl}")
    private String targetUploadUrl;

}
