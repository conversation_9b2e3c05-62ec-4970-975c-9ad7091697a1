package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 通用下拉选项项 DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OptionalItem<T extends Serializable> implements Serializable {
    private static final long serialVersionUID = 1L;
    private T key;
    private String value;
}