# 时间戳转换器使用说明

## TimestampToAreaTimeConverter

### 功能描述
`TimestampToAreaTimeConverter` 是一个自定义的EasyExcel转换器，用于将毫秒时间戳根据当前请求的区域ID转换为对应时区的 `yyyy-MM-dd HH:mm:ss` 格式时间。

### 工作原理
1. 通过 `RequestContextInfo.getAreaId()` 获取当前请求的区域ID
2. 使用 `IntlTimeUtil.parseTimestampToAreaTime()` 将时间戳转换为对应时区的格式化时间
3. 返回格式化的时间字符串

### 使用方法

#### 1. 在DTO字段上使用注解
```java
import com.alibaba.excel.annotation.ExcelProperty;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.seria.TimestampToAreaTimeConverter;

public class YourDTO {
    
    /**
     * 创建时间（毫秒时间戳）
     */
    @ExcelProperty(value = "Creation Time", converter = TimestampToAreaTimeConverter.class)
    private Long creationTime;
    
    /**
     * 完成时间（毫秒时间戳）
     */
    @ExcelProperty(value = "Completion Time", converter = TimestampToAreaTimeConverter.class)
    private Long completionTime;
}
```

#### 2. 替换现有的@DateTimeFormat注解
如果之前使用了 `@DateTimeFormat("yyyy-MM-dd HH:mm:ss")` 注解，可以替换为：
```java
// 原来的写法
@DateTimeFormat("yyyy-MM-dd HH:mm:ss")
private Long creationTime;

// 新的写法
@ExcelProperty(value = "Creation Time", converter = TimestampToAreaTimeConverter.class)
private Long creationTime;
```

### 优势
1. **自动时区转换**：根据请求的区域ID自动转换为对应时区的时间
2. **统一格式**：所有时间字段都使用 `yyyy-MM-dd HH:mm:ss` 格式
3. **异常处理**：当转换失败时返回空字符串，不会导致整个序列化失败
4. **日志记录**：转换失败时会记录详细的错误日志

### 注意事项
1. 确保 `RequestContextInfo.getAreaId()` 能正确获取到区域ID
2. 时间戳必须是毫秒级别的Long类型
3. 如果时间戳为null，序列化结果将为null
4. 如果转换过程中发生异常，将返回空字符串

### 示例输出
```json
{
  "creationTime": "2024-01-15 14:30:25",
  "completionTime": "2024-01-15 16:45:12"
}
```

### 相关依赖
- `com.xiaomi.nr.global.dev.base.RequestContextInfo`
- `com.mi.info.intl.retail.utils.intl.IntlTimeUtil`
- `com.alibaba.excel.annotation.ExcelProperty` 