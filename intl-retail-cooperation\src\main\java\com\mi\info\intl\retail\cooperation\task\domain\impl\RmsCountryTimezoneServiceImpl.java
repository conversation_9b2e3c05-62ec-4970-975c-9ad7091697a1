package com.mi.info.intl.retail.cooperation.task.domain.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mi.info.intl.retail.cooperation.task.domain.RmsCountryTimezoneService;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlCountryTimezoneService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 国家时区服务实现类
 * <AUTHOR>
 * @date 2024/03/21
 */
@Slf4j
@Service
public class RmsCountryTimezoneServiceImpl implements RmsCountryTimezoneService {

    @Resource
    private IntlCountryTimezoneService intlCountryTimezoneService;

    @Override
    public String getAreaCodeByCountryCode(String countryCode) {
        log.info("根据国家代码获取区域代码, countryCode: {}", countryCode);
        
        // 构建查询条件
        LambdaQueryWrapper<IntlRmsCountryTimezone> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlRmsCountryTimezone::getCountryCode, countryCode)
                   .last("LIMIT 1");
        
        // 查询数据
        IntlRmsCountryTimezone timezone = intlCountryTimezoneService.getOne(queryWrapper);
        
        if (timezone == null) {
            log.warn("未找到对应的区域代码, countryCode: {}", countryCode);
            return null;
        }
        
        return timezone.getAreaCode();
    }
} 