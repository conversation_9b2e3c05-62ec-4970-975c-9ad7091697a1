package com.mi.info.intl.retail.ldu.infra.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.ldu.infra.entity.IntlFileReportRel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface IntlFileReportRelMapper extends BaseMapper<IntlFileReportRel> {


    void batchInsertDatas(@Param("intlFileUploads") List<IntlFileReportRel> intlFileUploads);
}
