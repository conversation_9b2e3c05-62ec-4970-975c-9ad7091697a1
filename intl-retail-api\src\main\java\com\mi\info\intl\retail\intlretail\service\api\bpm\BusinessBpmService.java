package com.mi.info.intl.retail.intlretail.service.api.bpm;

import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.BpmApproveBusinessCodeEnum;

import java.util.Map;

/**
 * 回调的接口类
 *
 * <AUTHOR>
 */
public interface BusinessBpmService {

    void doCreate(Map<String, Object> map, BpmApproveBusinessCodeEnum businessCode, String emailPrefix,
                  String businessKey);

    void doTerminate(String businessKey, String emailPrefix, String comment);

    void detail();
}
