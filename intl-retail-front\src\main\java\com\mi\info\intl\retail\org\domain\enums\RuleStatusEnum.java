package com.mi.info.intl.retail.org.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 规则状态枚举
 */
@Getter
@AllArgsConstructor
public enum RuleStatusEnum {
    /**
     * 激活状态
     */
    ACTIVE(1, "active", "激活"),

    /**
     * 未激活状态
     */
    INACTIVE(2, "inactive", "未激活");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态英文描述
     */
    private final String status;

    /**
     * 状态中文描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举
     */
    public static RuleStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RuleStatusEnum value : RuleStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据状态获取枚举
     *
     * @param status 状态
     * @return 枚举
     */
    public static RuleStatusEnum getByStatus(String status) {
        if (status == null) {
            return null;
        }
        for (RuleStatusEnum value : RuleStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }
} 