package com.mi.info.intl.retail.org.domain.service.impl;

import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.dto.InspectionRecordDTO;
import com.mi.info.intl.retail.org.domain.repository.InspectionRecordRepository;
import com.mi.info.intl.retail.org.domain.service.InspectionRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 巡检记录服务实现类
 */
@Slf4j
@Service
public class InspectionRecordServiceImpl implements InspectionRecordService {

    @Autowired
    private InspectionRecordRepository inspectionRecordRepository;

    @Override
    public boolean saveInspectionRecord(InspectionRecordDomain inspectionRecordDomain) {
        log.info("保存巡检记录: {}", inspectionRecordDomain);
        return inspectionRecordRepository.save(inspectionRecordDomain);
    }

    @Override
    public boolean updateInspectionRecord(InspectionRecordDomain inspectionRecordDomain) {
        log.info("更新巡检记录: {}", inspectionRecordDomain);
        return inspectionRecordRepository.update(inspectionRecordDomain);
    }

    @Override
    public InspectionRecordDomain getInspectionRecordById(Long id) {
        log.info("根据ID获取巡检记录: {}", id);
        return inspectionRecordRepository.getById(id);
    }

    @Override
    public List<InspectionRecordDomain> getInspectionRecordsByBusinessCode(String businessCode) {
        log.info("通过阵地代码获取巡检记录列表: {}", businessCode);
        return inspectionRecordRepository.getByBusinessCode(businessCode);
    }

    @Override
    public InspectionRecordDomain getInspectionRecordByRuleCodeAndBusinessCode(String ruleCode, String businessCode) {
        log.info("通过规则编码和阵地代码获取巡检记录: ruleCode={}, businessCode={}", ruleCode, businessCode);
        return inspectionRecordRepository.getByRuleCodeAndBusinessCode(ruleCode, businessCode);
    }

    @Override
    public List<InspectionRecordDTO> getPendingInspectionsByCountries(List<String> countries) {
        log.info("通过国家列表查询未下发的巡检记录: {}", countries);
        
        if (countries == null || countries.isEmpty()) {
            log.warn("国家列表为空，返回空结果");
            return Collections.emptyList();
        }
        
        return inspectionRecordRepository.getPendingInspectionsByCountries(countries);
    }
} 