package com.mi.info.intl.retail.cooperation.downloadcenter.config;

import com.mi.info.intl.retail.cooperation.downloadcenter.factory.JobTriggerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 任务触发自动配置类
 * 自动注册业务模块配置，简化调用方的使用
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "job.trigger.enabled", havingValue = "true", matchIfMissing = true)
public class JobTriggerAutoConfiguration {

    @Resource
    private JobTriggerFactory jobTriggerFactory;

    /**
     * 自动注册LDU模块配置
     */
    @PostConstruct
    public void initLduModuleConfig() {
        try {
            JobTriggerFactory.BusinessModuleConfig lduConfig = 
                new JobTriggerFactory.BusinessModuleConfig(30L, "intl-retail-ldu", "LDU模块任务调度");
            jobTriggerFactory.registerModuleConfig("intl_retail", lduConfig);
            log.info("LDU模块配置自动注册成功");
        } catch (Exception e) {
            log.warn("LDU模块配置自动注册失败", e);
        }
    }

} 