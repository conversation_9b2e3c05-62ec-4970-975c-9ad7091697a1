package com.mi.info.intl.retail.intlretail.service.api.bpm.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BpmUser implements Serializable {

    private static final long serialVersionUID = -9007123152602376094L;
    private String userName;
    private String displayName;
    private String personId;
}
