package com.mi.info.intl.retail.cooperation.downloadcenter.example;

import cn.hutool.core.lang.ObjectId;
import cn.hutool.json.JSONUtil;
import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobSuccessDto;
import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobTriggerRequest;
import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobTriggerResponse;
import com.mi.info.intl.retail.cooperation.downloadcenter.enums.BusinessModuleEnum;
import com.mi.info.intl.retail.cooperation.downloadcenter.factory.JobTriggerFactory;
import com.mi.info.intl.retail.cooperation.downloadcenter.service.JobTriggerService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * LDU模块任务触发工具类
 * 通用任务触发能力
 * 注意：模块配置已通过自动配置类自动注册，无需手动配置
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Component
public class JobTriggerHelper {

    private static final String MODULE_NAME = "customer_business";
    public static final String TASK_TRIGGER_FAIL = "任务触发失败 ";

    @Resource
    private JobTriggerService jobTriggerService;

    @Resource
    private JobTriggerFactory jobTriggerFactory;

    /**
     * 触发LDU导出任务示例
     *
     * @param owner     任务负责人
     * @param url 任务参数
     * @return 任务触发结果
     */
    public CommonResponse<String> triggerCommonExportJob(String owner, String url) {
        CommonResponse<String> result = new CommonResponse<>("success");
        try {
            String taskParam =  JSONUtil.toJsonStr(JobSuccessDto.builder().fileUrl(url).build());
            // 构建任务请求
            JobTriggerRequest request = JobTriggerRequest.builder()
                    .taskParam(taskParam)
                    .taskDesc(BusinessModuleEnum.COMMON_EXPORT.getTaskDesc())
                    .taskName(BusinessModuleEnum.COMMON_EXPORT.getTaskName())
                    .jobKey(BusinessModuleEnum.COMMON_EXPORT.getJobKey())
                    .owner(owner)
                    .traceId(ObjectId.next())
                    .businessModule(BusinessModuleEnum.COMMON_EXPORT.getBusinessModule())
                    .projectId(BusinessModuleEnum.COMMON_EXPORT.getProjectId())
                    .projectName(BusinessModuleEnum.COMMON_EXPORT.getProjectName())
                    .build();

            log.info("触发通用导出任务, request:{}", JSONUtil.toJsonStr(request));

            // 触发任务
            JobTriggerResponse response = jobTriggerService.triggerJob(request);

            log.info("通用导出任务触发完成, response:{}", JSONUtil.toJsonStr(response));

            if (!"0".equals(response.getCode())) {
                result.setMessage(response.getMessage());
                result.setCode(Integer.parseInt(response.getCode()));
            }
            return result;
        } catch (Exception e) {
            log.error("触发通用导出任务失败", e);
            result.setCode(500);
            result.setMessage(TASK_TRIGGER_FAIL);
            return result;
        }
    }

    /**
     * 触发自定义业务导出任务示例
     *
     * @param owner     任务负责人
     * @param url 任务参数
     * @return 任务触发结果
     */
    public JobTriggerResponse triggerBusinessExportJob(String owner, String url) {
        try {
            String formatUrl =  JSONUtil.toJsonStr(JobSuccessDto.builder().fileUrl(url).build());
            // 构建任务请求
            JobTriggerRequest request = jobTriggerFactory.buildRequest(
                    MODULE_NAME,
                    BusinessModuleEnum.COMMON_EXPORT.getJobKey(),
                    owner,
                    formatUrl,
                    "business Job",
                    "Common Export Job"
            );

            log.info("触发business Job, request:{}", JSONUtil.toJsonStr(request));

            // 触发任务
            JobTriggerResponse response = jobTriggerService.triggerJob(request);

            log.info("business Job触发完成, response:{}", JSONUtil.toJsonStr(response));

            return response;

        } catch (Exception e) {
            log.error("触发business Job失败", e);
            return JobTriggerResponse.builder()
                    .code("500")
                    .message(TASK_TRIGGER_FAIL + e.getMessage())
                    .build();
        }
    }

} 