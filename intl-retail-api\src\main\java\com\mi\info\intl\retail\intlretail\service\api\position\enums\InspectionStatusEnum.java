package com.mi.info.intl.retail.intlretail.service.api.position.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.xiaomi.nr.global.dev.neptune.T;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Supplier;

/**
 * 巡检状态枚举值
 */
@Getter
@AllArgsConstructor
public enum InspectionStatusEnum implements I18nDesc {

    /**
     * 未下发
     */
    NOT_ISSUED(0, () -> T.tr("inspection.not_issued")),

    /**
     * 核验通过
     */
    VERIFICATION_PASSED(1, () -> T.tr("inspection.verification_passed")),

    /**
     * 核验未通过
     */
    VERIFICATION_FAILED(2, () -> T.tr("inspection.verification_rejected")),

    /**
     * 待核验
     */
    TO_BE_VERIFIED(3, () -> T.tr("inspection.to_be_verified")),

    /**
     * 未完成
     */
    NOT_COMPLETED(4, () -> T.tr("inspection.not_completed"));

    /**
     * 编码
     */
    @EnumValue
    private final Integer code;

    private final Supplier<String> i18nDesc;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static InspectionStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (InspectionStatusEnum value : InspectionStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
