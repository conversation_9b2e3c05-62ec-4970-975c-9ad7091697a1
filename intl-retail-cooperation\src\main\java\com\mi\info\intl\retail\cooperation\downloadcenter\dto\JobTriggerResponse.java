package com.mi.info.intl.retail.cooperation.downloadcenter.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 通用任务触发响应DTO
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobTriggerResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 响应码，0表示成功，非0表示失败
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 任务ID
     */
    private String jobId;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 扩展数据
     */
    private String extraData;
} 