package com.mi.info.intl.retail.org.domain.service;

import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.dto.InspectionRecordDTO;

import java.util.List;

/**
 * 巡检记录服务接口
 */
public interface InspectionRecordService {

    /**
     * 保存巡检记录
     *
     * @param inspectionRecordDomain 巡检记录领域对象
     * @return 是否保存成功
     */
    boolean saveInspectionRecord(InspectionRecordDomain inspectionRecordDomain);

    /**
     * 更新巡检记录
     *
     * @param inspectionRecordDomain 巡检记录领域对象
     * @return 是否更新成功
     */
    boolean updateInspectionRecord(InspectionRecordDomain inspectionRecordDomain);

    /**
     * 根据ID获取巡检记录
     *
     * @param id 巡检记录ID
     * @return 巡检记录领域对象
     */
    InspectionRecordDomain getInspectionRecordById(Long id);

    /**
     * 通过阵地代码获取巡检记录列表
     *
     * @param businessCode 阵地代码
     * @return 巡检记录领域对象列表
     */
    List<InspectionRecordDomain> getInspectionRecordsByBusinessCode(String businessCode);

    /**
     * 通过规则编码和阵地代码获取巡检记录
     *
     * @param ruleCode 规则编码
     * @param businessCode 阵地代码
     * @return 巡检记录领域对象
     */
    InspectionRecordDomain getInspectionRecordByRuleCodeAndBusinessCode(String ruleCode, String businessCode);

    /**
     * 通过国家列表查询未下发的巡检记录
     *
     * @param countries 国家列表
     * @return 未下发的巡检记录领域对象列表
     */
    List<InspectionRecordDTO> getPendingInspectionsByCountries(List<String> countries);
} 