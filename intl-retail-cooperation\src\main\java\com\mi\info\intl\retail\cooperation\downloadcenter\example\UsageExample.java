package com.mi.info.intl.retail.cooperation.downloadcenter.example;

import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobTriggerRequest;
import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobTriggerResponse;
import com.mi.info.intl.retail.cooperation.downloadcenter.factory.JobTriggerFactory;
import com.mi.info.intl.retail.cooperation.downloadcenter.service.JobTriggerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 任务触发能力使用示例
 * 展示如何在其他模块中简单使用任务触发能力
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Service
public class UsageExample {

    public static final String HANDLE_XXL_JOB = "lduExportHandleXxlJob";
    @Resource
    private JobTriggerService jobTriggerService;

    @Resource
    private JobTriggerFactory jobTriggerFactory;

    /**
     * 最简单的使用方式
     */
    public JobTriggerResponse simpleUsage(String owner, String taskParam) {
        try {
            // 使用工厂模式构建请求（推荐）
            JobTriggerRequest request = jobTriggerFactory.buildRequest(
                    "ldu",                    // 业务模块
                    HANDLE_XXL_JOB,  // 任务Key
                    owner,                    // 负责人
                    taskParam,                // 任务参数
                    "数据导出任务",             // 任务描述
                    "Export Job"              // 任务名称
            );

            // 触发任务
            return jobTriggerService.triggerJob(request);

        } catch (Exception e) {
            log.error("任务触发失败", e);
            return JobTriggerResponse.builder()
                    .code("500")
                    .message("任务触发失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 直接使用服务的方式
     */
    public JobTriggerResponse directUsage(String owner, String taskParam) {
        try {
            // 直接构建请求
            JobTriggerRequest request = JobTriggerRequest.builder()
                    .jobKey(HANDLE_XXL_JOB)
                    .owner(owner)
                    .taskParam(taskParam)
                    .taskDesc("数据导出任务")
                    .taskName("Export Job")
                    .projectId(30L)
                    .projectName("intl-retail-ldu")
                    .businessModule("ldu")
                    .build();

            // 触发任务
            return jobTriggerService.triggerJob(request);

        } catch (Exception e) {
            log.error("任务触发失败", e);
            return JobTriggerResponse.builder()
                    .code("500")
                    .message("任务触发失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 异步触发任务
     */
    public JobTriggerResponse asyncUsage(String owner, String taskParam) {
        try {
            JobTriggerRequest request = jobTriggerFactory.buildRequest(
                    "ldu",
                    HANDLE_XXL_JOB,
                    owner,
                    taskParam,
                    "异步数据导出任务",
                    "Async Export Job"
            );

            // 异步触发任务
            return jobTriggerService.triggerJobAsync(request);

        } catch (Exception e) {
            log.error("异步任务触发失败", e);
            return JobTriggerResponse.builder()
                    .code("500")
                    .message("异步任务触发失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 处理任务响应
     */
    public boolean handleJobResponse(JobTriggerResponse response) {
        if (response == null) {
            log.error("任务响应为空");
            return false;
        }

        log.info("任务响应: code={}, message={}, jobId={}",
                response.getCode(), response.getMessage(), response.getJobId());

        // 检查任务是否成功
        if ("0".equals(response.getCode())) {
            log.info("任务触发成功，任务ID: {}", response.getJobId());
            return true;
        } else {
            log.error("任务触发失败: {}", response.getMessage());
            return false;
        }
    }

    /**
     * 完整的任务触发流程示例
     */
    public boolean completeJobTriggerFlow(String owner, String taskParam) {
        try {
            log.info("开始任务触发流程，owner: {}, taskParam: {}", owner, taskParam);

            // 1. 触发任务
            JobTriggerResponse response = simpleUsage(owner, taskParam);

            // 2. 处理响应
            boolean success = handleJobResponse(response);

            // 3. 记录结果
            if (success) {
                log.info("任务触发流程完成，成功");
            } else {
                log.error("任务触发流程完成，失败");
            }

            return success;

        } catch (Exception e) {
            log.error("任务触发流程异常", e);
            return false;
        }
    }
} 