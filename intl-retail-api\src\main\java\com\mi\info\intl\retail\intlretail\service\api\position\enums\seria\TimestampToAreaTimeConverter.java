package com.mi.info.intl.retail.intlretail.service.api.position.enums.seria;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.xiaomi.nr.global.dev.base.RequestContextInfo;
import lombok.extern.slf4j.Slf4j;

/**
 * 时间戳转区域时间转换器
 * 用于EasyExcel导出时，将毫秒时间戳根据RequestContextInfo和IntlTimeUtil转换为对应时区的yyyy-MM-dd HH:mm:ss格式时间
 * 
 * <AUTHOR> Generated
 * @date 2024/01/15
 */
@Slf4j
public class TimestampToAreaTimeConverter implements Converter<Long> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Long.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Long value, ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {
        if (value == null || value == 0L) {
            return new WriteCellData<>("");
        }
        
        try {
            // 获取当前请求的区域ID
            String areaId = RequestContextInfo.getAreaId();
            
            // 使用IntlTimeUtil将时间戳转换为对应时区的格式化时间
            String formattedTime = IntlTimeUtil.parseTimestampToAreaTime(areaId, value);
            
            return new WriteCellData<>(formattedTime);
        } catch (Exception e) {
            log.error("时间戳转换失败: timestamp={}, error={}", value, e.getMessage(), e);
            // 发生异常时，返回空字符串
            return new WriteCellData<>("");
        }
    }
} 