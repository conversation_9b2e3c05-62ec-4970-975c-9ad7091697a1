---
description: 
globs: 
alwaysApply: false
---
# 项目结构
intl-retail
│
├── pom.xml (父POM文件)
│
├── intl-retail-api (对外API定义模块，不同领域对应的是不同的包名)
│   ├── src/main/java/com/mi/info/intl/retail/intlretail/service/api 文件路径：intl-retail-api/src/main/java/com/mi/info/intl/retail/intlretail/service/api
│   │   ├── retailer    (零售商相关API)
│   │   ├── inspection  (检查相关API)
│   │   ├── result      (结果相关API)
│   │   ├── request     (请求相关API)
│   │   ├── proxy       (代理相关API)
│   │   ├── mq          (消息队列相关API)
│   │   ├── job         (任务相关API)
│   │   ├── market      (市场相关API)
│   │   ├── messagepush (消息推送相关API)
│   │   ├── copilot     (AI助手相关API)
│   │   ├── fds         (文件存储相关API)
│   │   ├── store       (商店相关API)
│   │   └── demo        (示例API)
│
├── intl-retail-common (通用工具类模块)
│   ├── src/main/java/com/mi/info/intl/retail
│   │   ├── advice      (通知/增强相关)
│   │   │   └── excel   (Excel处理相关)
│   │   ├── constant    (常量定义)
│   │   ├── enums       (枚举定义)
│   │   ├── model       (模型定义)
│   │   └── utils       (工具类)
│
├── intl-retail-core (核心配置模块)
│   ├── src/main/java/com/mi/info/intl/retail/core
│   │   ├── config      (配置相关)
│   │   ├── exception   (异常处理)
│   │   └── utils       (核心工具类)
│
├── intl-retail-cooperation (协同模块)
│   ├── src/main/java/com/mi/info/intl/retail/cooperation
│   │   └── task        (任务相关)
│   │       ├── app     (应用层)
│   │       ├── component (组件)
│   │       ├── config    (配置)
│   │       ├── domain    (领域层)
│   │       ├── dto       (数据传输对象)
│   │       ├── infra     (基础设施层)
│   │       └── inspection (检查相关)
│
├── intl-retail-fieldforce (人力领域模块 领域子模块)
│   ├── src/main/java/com/mi/info/intl/retail
│   │   ├── attendance  (考勤相关)
│   │   └── user        (用户相关)
│   │       ├── app     (应用层)
│   │       ├── constant (常量)
│   │       ├── domain   (领域层)
│   │       └── infra    (基础设施层)
│
├── intl-retail-front (阵地领域模块 领域子模块)
│   ├── src/main/java/com/mi/info/intl/retail/org
│   │   ├── app         (应用层 主要负责服务编排)
│   │   ├── domain      (领域层 主要包含domain层和domainService层 包含最主要的业务逻辑 repository的接口)
│   │   └── infra       (基础设施层 repository的实现)
│
├── intl-retail-sales (销售/库存领域模块)
│
├── intl-retail-proxy (代理模块)
│   ├── intl-retail-app (应用层模块)
│   │   ├── src/main/java/com/mi/info/intl/retail/intlretail
│   │   │   ├── domain  (领域相关)
│   │   │   ├── service (服务相关)
│   │   │   │   └── app (应用层服务)
│   │   │   │       ├── copilot    (AI助手)
│   │   │   │       ├── demo       (示例)
│   │   │   │       ├── fds        (文件存储)
│   │   │   │       ├── inspection (检查)
│   │   │   │       ├── jobhandler (任务处理)
│   │   │   │       ├── market     (市场)
│   │   │   │       ├── messagepush (消息推送)
│   │   │   │       ├── proxy      (代理)
│   │   │   │       ├── retailer   (零售商)
│   │   │   │       ├── rpc        (远程调用)
│   │   │   │       └── store      (商店)
│   │   │   └── utils  (工具类)
│   │
│   ├── intl-retail-domain (领域层模块)
│   │   ├── src/main/java/com/mi/info/intl/retail/intlretail/domain
│   │   │   ├── common   (通用领域)
│   │   │   ├── http     (HTTP相关)
│   │   │   ├── order    (订单领域)
│   │   │   ├── product  (产品领域)
│   │   │   ├── push     (推送领域)
│   │   │   └── userdevice (用户设备领域)
│   │
│   └── intl-retail-infra (基础设施层模块)
│       ├── src/main/java/com/mi/info/intl/retail/intlretail/infra
│           ├── advice    (通知/增强)
│           ├── cache     (缓存)
│           ├── config    (配置)
│           ├── database  (数据库)
│           ├── http      (HTTP)
│           ├── mq        (消息队列)
│           ├── push      (推送)
│           ├── repository (仓储)
│           ├── rpc       (远程调用)
│           └── utils     (工具类)
│
└── intl-retail-server (服务启动模块)
    ├── src/main/java/com/mi/info/intl/retail/intlretail/app
        ├── aspect     (切面)
        ├── config     (配置)
        ├── consumer   (消费者)
        ├── controller (控制器)
        ├── convert    (转换器)
        ├── dto        (数据传输对象)
        ├── enums      (枚举)
        ├── event      (事件)
        ├── exception  (异常)
        ├── interceptor (拦截器)
        └── scheduler  (调度器)

# 代码实现要求
不仅需要实现接口，对应的接口实现类也要完善逻辑
# 代码示例
构建持久层代码需要构建
DO、mapper接口、repository接口及是类型 构建一个domain类
代码要在领域子模块中构建 比如需要在intl-retail-fieldforce模块下构建项目
数据访问使用的是mybatisplus框架
DO类
包路径为 com.mi.info.intl.retail.org.infra.entity 需要注意DO要放在entity
```java
/**
 * 国家时区表
 *
 * @TableName intl_rms_country_timezone
 */
@TableName(value = "intl_rms_country_timezone")
@Data
public class IntlRmsCountryTimezone implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private String countryTimezoneId;
    private String name;
    private String countryId;
    private String countryName;
    private String countryCode;
    private String timezoneName;
    private String timezoneCode;
    private String bias;
    private Integer stateCode;
    private String area;
    private String areaCode;
    private Long createdAt;
    private Long updatedAt;

}
```
mapper接口
mapper read接口
com.mi.info.intl.retail.org.infra.mapper.read;
```java
@Mapper
public interface IntlRmsCountryTimezoneReadMapper extends BaseMapper<IntlRmsCountryTimezone> {

}
```
mapper write类
com.mi.info.intl.retail.org.infra.mapper
```java
@Mapper
public interface IntlRmsCountryTimezoneMapper extends BaseMapper<IntlRmsCountryTimezone> {

}
```
对应的mapper xml生成 
intl-retail-front/src/main/resources/mapper
read也是同理 且路径为
intl-retail-front/src/main/resources/mapper/read
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.org.infra.mapper.read.IntlRmsCountryTimezoneReadMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone">
        <id property="id" column="id"/>
        <result property="countryTimezoneId" column="country_timezone_id"/>
        <result property="name" column="name"/>
        <result property="countryId" column="country_id"/>
        <result property="countryName" column="country_name"/>
        <result property="countryCode" column="country_code"/>
        <result property="timezoneName" column="timezone_name"/>
        <result property="timezoneCode" column="timezone_code"/>
        <result property="bias" column="bias"/>
        <result property="stateCode" column="state_code"/>
        <result property="area" column="area"/>
        <result property="areaCode" column="area_code"/>

    </resultMap>

    <sql id="Base_Column_List">
        id
        ,country_timezone_id,name,country_id,country_name,country_code,
        timezone_name,timezone_code,bias,state_code,area,
        area_code
    </sql>
</mapper>
```
repository接口
com.mi.info.intl.retail.org.domain
```java

public interface CompetitorRepository {
    List<IntlRmsCountryTimezoneDomain> getModelList(CompetitorModelListRequest request);

    List<CompetitorModelResponse> getCompetitorInfoWebList(CompetitorModelRequestDto request);
```
其中
IntlRmsCountryTimezoneDomain 为领域类 成员变量跟DO类保持一致
CompetitorModelResponse 为普通的DTO为了数据查询
repository实现
com.mi.info.intl.retail.org.infra.repository
```java
public class CompetitorRepositoryImpl implements CompetitorRepository {

    @Autowired
    private TblSalesCompetitorReadMapper salesReadMapper;

    @Autowired
    private TblCompetitorModelReadMapper modelReadMapper;
}
```
需要注意 Domain需要调整一下注解，如果是状态类型的提取成枚举，防止跟DO类重复度过高
分页查询 service
```java
@Override
    public SnakeCasePageInfoDto<ApprovalRecordDto> getPendingSelfieApprovalsByCurrentApproverWithinDays(
            Long currentApprover,
            List<ApprovalBusinessTypeEnum> businessTypes,
            Integer withinDays,
            Integer pageNum,
            Integer pageSize) {
        log.info("获取{}天内的待自拍审批记录(分页): currentApprover={}, businessTypes={}, pageNum={}, pageSize={}", 
                withinDays, currentApprover, businessTypes, pageNum, pageSize);
        
        // 将业务类型枚举转换为编码列表
        List<String> businessTypeCodes = businessTypes.stream()
                .map(ApprovalBusinessTypeEnum::getCode)
                .collect(Collectors.toList());
        
        // 设置状态为待审批
        List<Integer> statuses = Arrays.asList(ApproveStatusEnum.PENDING.getCode());
        
        // 计算N天前的时间戳
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(timezone));
        ZonedDateTime daysAgo = now.minusDays(withinDays);
        int startTime = (int) daysAgo.toEpochSecond();
        int endTime = (int) now.toEpochSecond();
        
        // 创建分页对象
        Page<ApprovalRecordDO> page = new Page<>(pageNum, pageSize);
        
        // 调用Repository查询
        Page<ApprovalRecordDO> recordPage = approvalRecordRepository.findPendingSelfieApprovalsByCurrentApproverWithinDaysPage(
                page, currentApprover, businessTypeCodes, statuses, startTime, endTime);
        
        // 将DO转换为DTO
        List<ApprovalRecordDto> recordDtos = recordPage.getRecords().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        
        // 创建分页响应对象
        SnakeCasePageInfoDto<ApprovalRecordDto> pageInfo = new SnakeCasePageInfoDto<>(pageNum,(int) recordPage.getSize(),(int)  recordPage.getTotal(), recordDtos);
        
        return pageInfo;
    }
```
repository 分页
```java
    @Override
    public Page<ApprovalRecordDO> findPendingSelfieApprovalsByCurrentApproverWithinDaysPage(
            Page<ApprovalRecordDO> page,
            Long currentApprover, 
            List<String> businessTypes, 
            List<Integer> statuses,
            Integer startTime,
            Integer endTime) {
        return approvalRecordMapper.findByCurrentApproverAndBusinessTypesAndStatusesAndTimeRangePage(
                page, currentApprover, businessTypes, statuses, startTime, endTime);
    }
```
mapper 接口 分页
```java
    Page<ApprovalRecordDO> findByCurrentApproverAndBusinessTypesAndStatusesAndTimeRangePage(
            Page<ApprovalRecordDO> page,
            @Param("currentApprover") Long currentApprover, 
            @Param("businessTypes") List<String> businessTypes, 
            @Param("statuses") List<Integer> statuses,
            @Param("startTime") Integer startTime,
            @Param("endTime") Integer endTime);
```
mapper xml 分页
```xml
    <select id="findByCurrentApproverAndBusinessTypesAndStatusesAndTimeRangePage" resultType="com.mi.info.india.sales.b2c.smc.operation.approve.model.ApprovalRecordDO">
        SELECT
        <include refid="Base_Column_List" />
        FROM approval_records
        WHERE current_approver = #{currentApprover}
        <if test="businessTypes != null and businessTypes.size() > 0">
            AND business_type IN
            <foreach collection="businessTypes" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="statuses != null and statuses.size() > 0">
            AND status IN
            <foreach collection="statuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="startTime != null">
            AND created_at >= #{startTime}
        </if>
        <if test="endTime != null">
            AND created_at &lt;= #{endTime}
        </if>
        ORDER BY created_at DESC
    </select>
```
## 分页查询
使用myabtisPlus框架进行分页查询,需要mapper完成对应的联表查询
除了特殊要求 联表查禁止有复杂转换
字段的转换可以发在代码中进行

DomainService不能使用响应体的类 返回的dto应该更通用