package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 图片集合
 * <AUTHOR>
 * @Date 2025/7/9 21:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PhotoGroup implements Serializable {
    private static final long serialVersionUID = 1L;
    private String name;
    private String guid;
    private List<String> images;
}
