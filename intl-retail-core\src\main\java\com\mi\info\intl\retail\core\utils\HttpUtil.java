package com.mi.info.intl.retail.core.utils;

import kong.unirest.HttpResponse;
import kong.unirest.Unirest;

import java.util.HashMap;
import java.util.Map;

public class HttpUtil {

    public static String post(String url, String body) {
        HttpResponse<String> response = Unirest.post(url)
                .header("accept", "application/json")
                .body(body)
                .asString();
        if (response.getStatus() == 200) {
            return response.getBody();
        } else {
            return "";
        }
    }

    public static String post(String url, Map<String, String> params) {
        HttpResponse<String> response = Unirest.post(url)
                .header("accept", "application/json")
                .fields(new HashMap<>(params))
                .asString();
        if (response.getStatus() == 200) {
            return response.getBody();
        } else {
            return "";
        }
    }
}
