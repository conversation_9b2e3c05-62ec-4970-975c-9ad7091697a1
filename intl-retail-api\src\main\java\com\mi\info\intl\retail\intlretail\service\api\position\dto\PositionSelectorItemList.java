package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 阵地巡检筛选项返回对象
 */
@Data
public class PositionSelectorItemList implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 阵地类型
     */
    private List<OptionalItem<String>> positionType;
    /**
     * 阵地品类
     */
    private List<OptionalItem<String>> positionCategory;
    /**
     * 阵地建设类型
     */
    private List<OptionalItem<Integer>> positionConstructionType;
    /**
     * 任务状态
     */
    private List<OptionalItem<Integer>> taskStatus;
    /**
     * 巡检状态
     */
    private List<OptionalItem<Integer>> inspectionStatus;
    /**
     * 阵地位置
     */
    private List<OptionalItem<String>> positionLocation;

    /**
     * 拒绝原因
     */
    private List<OptionalItem<Integer>> disapproveReason;

    /**
     * 是否阵地范围内
     */
    private List<OptionalItem<Integer>> storeLimitedRange;

    /**
     * 阵地展陈标准化打标信息
     */
    private List<OptionalItem<String>> displayStandardization;
}