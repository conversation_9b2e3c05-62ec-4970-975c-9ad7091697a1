package com.mi.info.intl.retail.org.domain.util;

import com.xiaomi.cnzone.commons.utils.StringUtils;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Optional;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@UtilityClass
public class ContextUtil {

    public String getLang() {
        String language = RpcContext.getContext().getAttachment("$language");
        if (StringUtils.isEmpty(language)) {
            language = getHeader("X-Retail-Language");
        }
        if (StringUtils.isEmpty(language)) {
            log.error("language is empty, use default: en-US");
            language = "en-US";
        }
        return language;
    }

    public String getArea() {
        String areaId = RpcContext.getContext().getAttachment("$area_id");
        if (StringUtils.isEmpty(areaId)) {
            areaId = getHeader("X-Retail-Locale");
        }
        if (StringUtils.isEmpty(areaId)) {
            log.error("areaId is empty, use default: SG");
            areaId = "SG";
        }
        return areaId;
    }

    public Optional<HttpServletRequest> getRequest() {
        return Optional.ofNullable(RequestContextHolder.getRequestAttributes())
                .filter(ServletRequestAttributes.class::isInstance).map(ServletRequestAttributes.class::cast)
                .map(ServletRequestAttributes::getRequest);
    }

    public String getHeader(String key) {
        return getRequest().map(r -> r.getHeader(key)).orElse(null);
    }
}
