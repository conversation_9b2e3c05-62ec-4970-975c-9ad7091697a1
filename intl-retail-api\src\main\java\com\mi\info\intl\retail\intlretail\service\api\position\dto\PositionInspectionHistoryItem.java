package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class PositionInspectionHistoryItem implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long operationTime;
    private String operator;
    private Integer operationType;
    private String operationTypeDesc;
    private String remark;
    private Integer disapproveReason;
    private String disapproveReasonDesc;
}