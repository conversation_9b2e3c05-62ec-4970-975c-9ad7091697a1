package com.mi.info.intl.retail.intlretail.service.api.position.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 阵地巡检提交请求DTO
 */
@Data
public class PositionInspectionSubmitRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 阵地巡检ID
     */
    private Long positionInspectionId;

    /**
     * 门店大门照片
     */
    private PhotoGroup storeGate;

    /**
     * 阵地落位照片
     */
    private PhotoGroup positionLandingPhoto;

    /**
     * 阵地展示照片
     */
    private PhotoGroup positionDisplay;

    /**
     * 家具照片列表
     */
    private List<FurniturePhotoGroup> furniturePictures;

    /**
     * 阵地是需要完成巡检任务
     */
    private Boolean noNeedToComplete;
    /**
     * 当前登陆人Mi ID
     */
    private Long currentUserMiId;
    /**
     * 登录人纬度
     */
    private Double latitude;

    /**
     * 登录人经度
     */
    private Double longitude;

    /**
     * 阵地打卡纬度
     */
    private Double positionLatitude;

    /**
     * 阵地打卡经度
     */
    private Double positionLongitude;

    /**
     * 打卡配置距离(米)
     */
    private Double checkInDistance;

    /**
     * 巡检负责人Account
     */
    private String owner;

}
