package com.mi.info.intl.retail.intlretail.service.api.position;

import com.mi.info.intl.retail.intlretail.service.api.position.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterFinishTaskReq;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.PageResponse;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionHistoryItem;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.PositionInspectionResponsiblePersonDTO;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 阵地巡检的服务入口
 * 支持APP+PC
 */
public interface PositionInspectionService {


    /**
     * 分页查询阵地巡检信息
     *
     * @param request
     * @return
     */
    CommonApiResponse<PageResponse<PositionInspectionItemDTO>> listPositionInspectionGateWay(PositionInspectionRequest request);

    /**
     * 查询阵地巡检列表
     *
     * @param request 请求参数
     * @return 阵地巡检列表响应
     */
    PageResponse<PositionInspectionItem> listPositionInspection(PositionInspectionRequest request);

    /**
     * 阵地巡检提交
     *
     * @param request 请求参数
     * @return 阵地巡检提交响应
     */
    CommonApiResponse<String> submitPositionInspection(PositionInspectionSubmitRequest request);

    /**
     * 阵地巡检详情App端查询
     *
     * @param request 请求参数-阵地巡检id
     * @return 阵地巡检详情App内容响应
     */
    CommonApiResponse<PositionInspectionDetailResponse> getPositionInspectionDetail(@Valid @NotNull PositionInspectionDetailRequest request);

    /**
     * 阵地巡检明细PC端查询
     *
     * @param request 请求参数-阵地巡检id
     * @return 阵地巡检详情PC内容响应
     */
    CommonApiResponse<PositionInspectionAllDetailDTO> getPositionInspectionAllDetail(@Valid @NotNull PositionInspectionDetailRequest request);

    /**
     * 获取阵地巡检筛选项列表
     *
     * @param request 请求
     * @return 各筛选项的下拉列表
     */
    CommonApiResponse<PositionSelectorItemList> getSelectorList(PositionItemListRequest request);

    /**
     * 审批阵地巡检
     *
     * @param request 审批请求参数
     * @return 审批结果
     */
    CommonApiResponse<String> approvePositionInspection(PositionInspectionApproveRequest request);

    /**
     * 获取巡检记录汇总数据网关
     *
     * @param request 请求参数
     * @return 巡检记录汇总数据响应
     */
    CommonApiResponse<InspectionSummaryDTO> summaryGateWay(PositionInspectionRequest request);

    /**
     * 获取巡检记录汇总数据
     *
     * @param request 阵地巡检请求DTO
     * @return 巡检记录汇总数据响应
     */
    InspectionSummaryDTO summary(PositionInspectionRequest request);

    /**
     * 标记任务为无需完成
     *
     * @param req 任务中心完成任务请求
     * @return 操作结果
     */
    CommonApiResponse<String> noNeedCompleteTask(TaskCenterFinishTaskReq req);

    /**
     * 完成用户当前任务动作
     *
     * @param req 任务中心完成任务请求
     * @return 操作结果
     */
    CommonApiResponse<String> outerTaskFinish(TaskCenterFinishTaskReq req);


    /**
     * 查询阵地巡检操作历史
     *
     * @param positionInspectionId 阵地巡检ID
     * @return 操作历史列表
     */
    CommonApiResponse<List<PositionInspectionHistoryItem>> operationHistory(
            @Valid @NotNull PositionInspectionHistoryRequest positionInspectionId);

    /**
     * 根据业务代码获取照片链接JSON
     *
     * @param businessCode 业务代码
     * @return 包含照片链接的JSON字符串响应
     */
    CommonApiResponse<List<ImageCenterDto>> getPhotosByBusinessCode(String businessCode);

    /**
     * 根据阵地代码获取负责人信息
     *
     * @param positionCode 阵地代码
     * @return 负责人信息
     */
    CommonApiResponse<PositionInspectionResponsiblePersonDTO> getResponsiblePersonByPositionCode(String positionCode);


    /**
     * 根据阵地编码创建巡检记录
     *
     * @param positionCode 阵地编码
     * @param operatorId   操作人ID
     * @return 创建结果
     */
    CommonApiResponse<String> createInspectionByPositionCode(String areaId, String positionCode, String operatorId);

    /**
     * 查询阵地家具列表
     *
     * @param request 阵地家具查询请求参数
     * @return 阵地家具列表响应
     */
    CommonApiResponse<List<OptionalItem<Integer>>> getPositionFurnitureList(@Valid @NotNull PositionFurnitureRequest request);


    /**
     * 定时任务提醒
     *
     * @return 处理结果
     */
    CommonApiResponse<String> taskReminder();

    /**
     * 批量上传弹窗
     *
     * @param request 批量上传请求
     * @return 上传结果
     */
    CommonApiResponse<Void> batchUpload(PositionImgBatchUploadRequest request);

    /**
     * 导出阵地巡检数据到Excel
     *
     * @param request 阵地巡检请求参数
     * @return 包含下载URL的响应
     */
    CommonApiResponse<PositionInspectionExportResponse> exportPositionInspection(PositionInspectionRequest request);

    /**
     * 阵地巡检任务下发
     * 流程：1.获取当前时间为凌晨0点的国家列表
     * 2.获取这些国家未下发的巡检任务
     * 3.获取门店负责人并更新巡检任务
     * 4.下发任务
     *
     * @return 处理结果
     */
    CommonApiResponse<String> dispatchInspectionTasks(Integer regularTime);

    CommonApiResponse<List<OptionalItem<Integer>>> getAbnormalReason();
}
