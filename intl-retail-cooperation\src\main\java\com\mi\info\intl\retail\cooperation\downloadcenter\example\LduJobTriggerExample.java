package com.mi.info.intl.retail.cooperation.downloadcenter.example;

import cn.hutool.json.JSONUtil;
import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobTriggerRequest;
import com.mi.info.intl.retail.cooperation.downloadcenter.dto.JobTriggerResponse;
import com.mi.info.intl.retail.cooperation.downloadcenter.factory.JobTriggerFactory;
import com.mi.info.intl.retail.cooperation.downloadcenter.service.JobTriggerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * LDU模块任务触发示例
 * 展示如何使用通用任务触发能力
 * 注意：模块配置已通过自动配置类自动注册，无需手动配置
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Component
public class LduJobTriggerExample {

    private static final String LDU_MODULE = "ldu";
    public static final String TASK_TRIGGER_FAIL = "任务触发失败: ";

    @Resource
    private JobTriggerService jobTriggerService;

    @Resource
    private JobTriggerFactory jobTriggerFactory;

    /**
     * 触发LDU导出任务示例
     *
     * @param owner     任务负责人
     * @param taskParam 任务参数
     * @return 任务触发结果
     */
    public JobTriggerResponse triggerLduExportJob(String owner, String taskParam) {
        try {
            // 构建任务请求
            JobTriggerRequest request = jobTriggerFactory.buildRequest(
                    LDU_MODULE,
                    "lduExportHandleXxlJob",
                    owner,
                    taskParam,
                    "LDU数据导出任务",
                    "LDU Export Job"
            );

            log.info("触发LDU导出任务, request:{}", JSONUtil.toJsonStr(request));

            // 触发任务
            JobTriggerResponse response = jobTriggerService.triggerJob(request);

            log.info("LDU导出任务触发完成, response:{}", JSONUtil.toJsonStr(response));

            return response;

        } catch (Exception e) {
            log.error("触发LDU导出任务失败", e);
            return JobTriggerResponse.builder()
                    .code("500")
                    .message(TASK_TRIGGER_FAIL + e.getMessage())
                    .build();
        }
    }

    /**
     * 触发LDU数据处理任务示例
     *
     * @param owner     任务负责人
     * @param taskParam 任务参数
     * @return 任务触发结果
     */
    public JobTriggerResponse triggerLduProcessJob(String owner, String taskParam) {
        try {
            // 构建任务请求
            JobTriggerRequest request = jobTriggerFactory.buildRequest(
                    LDU_MODULE,
                    "lduProcessHandleXxlJob",
                    owner,
                    taskParam,
                    "LDU数据处理任务",
                    "LDU Process Job"
            );

            log.info("触发LDU数据处理任务, request:{}", JSONUtil.toJsonStr(request));

            // 触发任务
            JobTriggerResponse response = jobTriggerService.triggerJob(request);

            log.info("LDU数据处理任务触发完成, response:{}", JSONUtil.toJsonStr(response));

            return response;

        } catch (Exception e) {
            log.error("触发LDU数据处理任务失败", e);
            return JobTriggerResponse.builder()
                    .code("500")
                    .message(TASK_TRIGGER_FAIL + e.getMessage())
                    .build();
        }
    }

    /**
     * 直接使用JobTriggerService的示例
     *
     * @param owner     任务负责人
     * @param taskParam 任务参数
     * @return 任务触发结果
     */
    public JobTriggerResponse triggerJobDirectly(String owner, String taskParam) {
        try {
            // 直接构建请求，不依赖工厂
            JobTriggerRequest request = JobTriggerRequest.builder()
                    .jobKey("lduExportHandleXxlJob")
                    .owner(owner)
                    .taskParam(taskParam)
                    .taskDesc("LDU数据导出任务")
                    .taskName("LDU Export Job")
                    .projectId(30L)
                    .projectName("intl-retail-ldu")
                    .businessModule("ldu")
                    .build();

            log.info("直接触发任务, request:{}", JSONUtil.toJsonStr(request));

            // 触发任务
            JobTriggerResponse response = jobTriggerService.triggerJob(request);

            log.info("任务触发完成, response:{}", JSONUtil.toJsonStr(response));

            return response;

        } catch (Exception e) {
            log.error("直接触发任务失败", e);
            return JobTriggerResponse.builder()
                    .code("500")
                    .message(TASK_TRIGGER_FAIL + e.getMessage())
                    .build();
        }
    }
} 