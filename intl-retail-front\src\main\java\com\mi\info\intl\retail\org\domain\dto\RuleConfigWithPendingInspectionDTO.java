package com.mi.info.intl.retail.org.domain.dto;

import lombok.Data;

/**
 * 规则配置和未下发巡检记录DTO
 */
@Data
public class RuleConfigWithPendingInspectionDTO {

    /**
     * 规则配置ID
     */
    private Long id;

    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则状态
     */
    private Integer ruleStatus;

    /**
     * 区域
     */
    private String region;

    /**
     * 国家
     */
    private String country;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 拍照是否允许相册选择：1-是，0-否
     */
    private Integer allowPhotoFromGallery;

    /**
     * 未下发的巡检记录数量
     */
    private Integer pendingInspectionCount;
} 