package com.mi.info.intl.retail.org.domain.dto;

import com.mi.info.intl.retail.intlretail.service.api.position.enums.ConstructionType;
import lombok.Data;

import java.io.Serializable;

/**
 * 巡检记录DTO，包含规则配置信息
 */
@Data
public class InspectionRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;
    /**
     *ID
     */
    private Long ruleId;

    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 业务唯一编码 阵地代码
     */
    private String businessCode;

    /**
     * 业务品类 职位建设类型
     */
    private ConstructionType businessType;

    /**
     * 国家
     */
    private String country;

    /**
     * 阵地新建/升级时间
     */
    private Long businessCreationTime;

    /**
     * 建设动作编码
     */
    private String constructionActionCode;

    /**
     * 任务状态
     */
    private Integer taskStatus;

    /**
     * 任务完成时间
     */
    private Long taskCompletionTime;

    /**
     * 巡检状态
     */
    private Integer inspectionStatus;

    /**
     * 验证时间
     */
    private Long verificationTime;

    /**
     * 巡检负责人账号
     */
    private String inspectionOwner;

    /**
     * 巡检负责人MiID
     */
    private Long inspectionOwnerMiId;

    /**
     * 审批状态
     */
    private Integer verifyStatus;

    /**
     * 任务下发时间
     */
    private Long taskCreateInstanceTime;

    /**
     * 提醒时间
     */
    private Long reminderTime;

    // 规则配置相关字段
    /**
     * 任务定义ID
     */
    private long taskDefId;

    /**
     * 任务批次ID
     */
    private long taskBatchId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则状态
     */
    private Integer ruleStatus;


    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 是否允许从相册选择照片
     */
    private Boolean allowPhotoFromGallery;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private Long creationTime;

    /**
     * 修改者
     */
    private String modifier;

    /**
     * 修改时间
     */
    private Long modificationTime;

    /**
     * 提醒天数
     */
    private Integer reminderDays;
}