package com.mi.info.intl.retail.org.domain.service.impl;

import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.domain.dto.RuleConfigWithPendingInspectionDTO;
import com.mi.info.intl.retail.org.domain.repository.RuleConfigRepository;
import com.mi.info.intl.retail.org.domain.service.RuleConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 规则配置服务实现类
 */
@Slf4j
@Service
public class RuleConfigServiceImpl implements RuleConfigService {

    @Autowired
    private RuleConfigRepository ruleConfigRepository;

    @Override
    public boolean saveRuleConfig(RuleConfigDomain ruleConfigDomain) {
        log.info("保存规则配置: {}", ruleConfigDomain);
        return ruleConfigRepository.save(ruleConfigDomain);
    }

    @Override
    public boolean updateRuleConfig(RuleConfigDomain ruleConfigDomain) {
        log.info("更新规则配置: {}", ruleConfigDomain);
        return ruleConfigRepository.update(ruleConfigDomain);
    }

    @Override
    public RuleConfigDomain getRuleConfigById(Long id) {
        log.info("根据ID查询规则配置: {}", id);
        return ruleConfigRepository.getById(id);
    }

    @Override
    public RuleConfigDomain getRuleConfigByRuleCode(String ruleCode) {
        log.info("根据规则编码查询规则配置: {}", ruleCode);
        return ruleConfigRepository.getByRuleCode(ruleCode);
    }

    @Override
    public List<RuleConfigWithPendingInspectionDTO> getActiveRulesWithPendingInspections(List<String> countries) {
        log.info("查询国家列表中有效的规则配置和未下发的巡检记录: {}", countries);
        
        if (countries == null || countries.isEmpty()) {
            log.warn("国家列表为空，返回空结果");
            return Collections.emptyList();
        }
        
        return ruleConfigRepository.getActiveRulesWithPendingInspections(countries);
    }
} 