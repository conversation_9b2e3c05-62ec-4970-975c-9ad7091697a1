package com.mi.info.intl.retail.cooperation.downloadcenter.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 通用任务触发请求DTO
 * 支持不同业务模块的任务调度需求
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobTriggerRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务参数
     */
    private String taskParam;

    /**
     * 任务描述
     */
    private String taskDesc;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务Key
     */
    private String jobKey;

    /**
     * 任务负责人
     */
    private String owner;

    /**
     * 追踪ID
     */
    private String traceId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 业务模块标识
     */
    private String businessModule;

    /**
     * 扩展参数
     */
    private String extraParams;
} 