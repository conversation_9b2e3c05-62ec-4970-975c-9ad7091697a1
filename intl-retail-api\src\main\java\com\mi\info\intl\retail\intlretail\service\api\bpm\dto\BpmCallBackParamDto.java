package com.mi.info.intl.retail.intlretail.service.api.bpm.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BpmCallBackParamDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer taskId;
    private String businessKey;
    private String modelCode;
    private String taskName;
    private String comment;
    private String taskDefinitionKey;
    private String delegationState;
    private String status;
    private BpmUser operator;
    private BpmUser assignee;



}
