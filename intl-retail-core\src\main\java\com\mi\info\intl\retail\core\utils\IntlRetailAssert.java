package com.mi.info.intl.retail.core.utils;

import com.mi.info.intl.retail.core.exception.RetailRunTimeException;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collection;

@UtilityClass
public class IntlRetailAssert extends Assert {

    public static void isNull(Object object, String message) {
        if (object != null) {
            throw new RetailRunTimeException(message);
        }
    }

    public <T> T nonNull(T reference, String errorMessage) {
        checkArgument(reference != null, errorMessage);
        return reference;
    }

    public String notEmpty(String reference, String errorMessage) {
        checkArgument(StringUtils.isNotEmpty(reference), errorMessage);
        return reference;
    }

    public void checkArgument(boolean expression, String errorMessage) {
        if (!expression) {
            throw new RetailRunTimeException(errorMessage);
        }
    }

    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new RetailRunTimeException(message);
        }
    }
    public static void notEmpty(Collection<?> collection, String message) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new RetailRunTimeException(message);
        }
    }

}
